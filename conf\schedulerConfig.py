class schedulerConfig:
    # 是否开启api查看定时任务的配置
    # 设置为True后，可以通过SCHEDULER_API_PREFIX接口访问定时任务配置界面
    SCHEDULER_API_ENABLED = True
    # 若开启api后访问接口后端报错KeyError: 'JSONIFY_PRETTYPRINT_REGULAR'，可在配置中添加布尔类型的JSONIFY_PRETTYPRINT_REGULAR，值设置为True或False都可以，设置为True仅会在前端界面以更美观的方式显示内容。
    JSONIFY_PRETTYPRINT_REGULAR = True
    # 设置访问查看定时任务配置的api接口
    # SCHEDULER_API_PREFIX: str (default: "/scheduler")
    # # 调度程序endpoint前缀
    # SCHEDULER_ENDPOINT_PREFIX: str (default: "scheduler.")
    # # 允许访问调度器的主机
    # SCHEDULER_ALLOWED_HOSTS: list (default: ["*"])
