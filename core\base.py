import logging
from typing import Dict
import pymysql

from conf.systemConf import *
from dbutils.pooled_db import PooledDB
from log import Logger

# 注册全局日志组件
global logger
logger = Logger()

# 连接池工厂（单例）
class connFactory:

    # 当实例化一个对象时，先调用__new__方法（未定义时调用object.__new__）实例化对象，然后调用__init__方法进行对象初始化
    # 所以，声明一个私有类变量__instance，当__instance不为None时，表示系统中已有实例，直接返回该实例；若__instance为None时，表示系统中还没有该类的实例，则创建新实例并返回。

    __instance = None
 
    # 自定义实例化方法
    def __new__(cls, *args, **kwargs):
        if not cls.__instance:
            cls.__instance = super().__new__(cls, *args, **kwargs)
        return cls.__instance


    # 连接池字典
    connPoolDic: Dict[str, PooledDB] = {}

    # 初始化方法
    def __init__(self):
        if self.connPoolDic:
           return 
        # 生成各配置的连接池
        self.connPoolDic['default'] = self.buildPool('')
        for env_name in EnvConf.MEMBER_CONF.keys():
            self.connPoolDic[env_name] = self.buildPool(env_name)
        return

    # 根据环境名称，生陈对应的连接池
    def buildPool(self, env_name) -> PooledDB:
        if len(env_name) == 0:
            dbHost = EnvConf.DEFAULT_DB_CONG.get('host')
            dbPort = EnvConf.DEFAULT_DB_CONG.get('port')
            dbName = EnvConf.DEFAULT_DB_CONG.get('dbName')
            userName = EnvConf.DEFAULT_DB_CONG.get('userName')
            password = EnvConf.DEFAULT_DB_CONG.get('password')
        else :
            memberConf = EnvConf.MEMBER_CONF.get(env_name)
            if memberConf is None:
                logging.error('无 {} 的环境配置'.format(env_name))
                return None

            env = memberConf.get('DB')
            if env is None:
                logging.error('无 {} 的环境数据库配置'.format(env_name))
                return None

            dbHost = env.get('host')
            dbPort = env.get('port')
            dbName = env.get('dbName')
            userName = env.get('userName')
            password = env.get('password')

        pool = PooledDB(
            creator = pymysql,  # 使用 pymysql 作为数据库连接库
            maxconnections = 10,  # 连接池大小为 10
            host = dbHost,
            port = dbPort,
            user = userName,
            password = password,
            database = dbName,
            charset = 'utf8mb4',
            setsession=['SET AUTOCOMMIT = 1']
        )

        return pool


# 数据仓储基类
class baseRepository:

    # 数据库连接
    db: lambda: None

    def __init__(self, name=''):
        if len(name) == 0:
            pool = connFactory().connPoolDic['default']
            self.db = pool.connection()
        else:
            pool = connFactory().connPoolDic[name]
            self.db = pool.connection()
    
    def getOrderAndLimit(self, reqVars):
        """
        生成排序和分页条件
        :param reqVars: 请求参数字典
        """
        orderField = ' order by id desc '

        pageSize = int(reqVars['limit']) if 'limit' in reqVars.keys() and reqVars['limit'] else 20
        if 'page' in reqVars.keys() and reqVars['page']:
            orderField += 'limit {},{}'.format(int(reqVars['page']) * pageSize - pageSize, pageSize)
        else:
            orderField += 'limit ' + str(pageSize)

        return orderField


# 基础响应结构
class baseRespone:

    def __init__(self, requestid):
        self.code = '40000'
        self.msg = 'Complex Error'
        self.polyapitotalms = 1
        self.polyapirequestid = requestid
        self.subcode = 'LXO.REQUEST_FAILURE.BATCHERROR'
        self.submessage = ''
        self.messagefrom = 'POLY'
        self.env = '1'
        self.ishasnextpage = False
        self.numtotalorder = 0
        self.nexttoken = ''
        self.isencrypted = 0
        self.querytype = '0'


# json对象
class JSONObject:

    def __init__(self, d):
        for name, value in d.items():
            # name 名称取小写
            self.__dict__[name.lower()] = value
