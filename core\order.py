import time
import copy
import logging

import requests

from conf.systemConf import *
from models.jackyunclient import jackyunClient
from utils.randomMaker import *
from utils.jsonHelper import *
from utils.signHelper import signHelper

# 推单工具类
class orderUtils:

    @staticmethod
    def baseProcess(order, platId, fix_time):
        nowTimeStr = time.strftime('%Y-%m-%d %H:%M:%S')
        # 生成订单号
        order.platorderno = randomMaker.makeRandomNo()
        # 时间修正
        if fix_time:
            order.tradetime = nowTimeStr
            order.modifytime = nowTimeStr
            order.paytime = nowTimeStr
            order.collagetime = nowTimeStr
        # order.sellerorderid = randomMaker.makeRandom(15)
        # 状态修正
        if order.tradestatus == 'JH_99':
            order.tradestatus = 'JH_02'

        return

    # 根据商品模板信息生成订单商品信息填充
    @staticmethod
    def makeOrderGoods(order, templateGoodsList):
        if type(templateGoodsList) is not list:
            #print("goodsList 必须为数组类型")
            return order.goodinfos
        if len(templateGoodsList) == 0:
            #print("goodsList 不能为空")
            return order.goodinfos
        if len(order.goodinfos) == 0:
            #print("订单商品 不能为空")
            return order.goodinfos

        result = []
        goodsBase = order.goodinfos[0]
        # 根据入参生成商品信息
        for item in templateGoodsList:
            d = copy.deepcopy(goodsBase)
            # 固定字段赋值
            d.tradegoodsno = item.out_id
            d.outitemid = item.out_id
            d.outskuid = item.out_sku_id
            d.tradegoodsname = item.title
            if hasattr(item, 'count'):
                d.goodscount = item.count
            result.append(d)
            # 扩展字段赋值
            if len(item.extra) == 0:
                continue
            try:
                extraDto = jsonHelper.deJson(item.extra)
                if extraDto is None:
                    continue
                for attr in dir(extraDto):
                    if attr.startswith('_'):
                        continue
                    val = getattr(extraDto, attr)
                    setattr(d, attr, val)
            except Exception as e:
                ret = "扩展字段赋值失败 : " + str(e)
                logging.error(ret)

        return result


    # 推单静态方法
    @staticmethod
    def pushOrder(memberName, interface, platId, shopId, orderObj):

        # 生成请求id
        requestid = randomMaker.makeRequestId()

        orderResponse = baseRespone(requestid)
        orderResponse.code = '10000'
        orderResponse.msg = 'SUCCESS'
        orderResponse.orders = [orderObj]

        memberConf = EnvConf.MEMBER_CONF.get(memberName)
        if memberConf is None:
            logging.error('无 {} 的环境配置'.format(memberName))
            return
        
        env = memberConf.get('PUSH')
        if env is None:
            logging.error('无 {} 的推单配置'.format(memberName))
            return

        try:
            # 数据整合
            orderResponse.businessShopID = shopId
            orderResponse.businessMemberName = memberName
            orderResponse.apiPlat = platId
            for order in orderResponse.orders:
                order.salechannelId = shopId

            data = dict()
            data['jsonStr'] = jsonHelper.toJson(orderResponse)
            if interface == 'online' :
                data['jsonStr'] = data['jsonStr'].replace('false', '"0"')

            # 模拟客户端登陆
            jackyunclient = jackyunClient(env.get('url'))
            jackyunclient.login(memberName, env.get('code'), env.get('user'), env.get('pwd'), False)
            jackyunclient.__loginCache = {}
            # 抓单结果推送
            rsp = jackyunclient.httpPost('/jkyun/omsapi-order/saveorder', data)

            return rsp

        except Exception as e:
            ret = "推单失败 : " + str(e)
            logging.error(ret)

    # 消息推送静态方法
    @staticmethod
    def pushNotice(memberName, platId, shopId, shopToken, messageType, messageContent):

        memberConf = EnvConf.MEMBER_CONF.get(memberName)
        if memberConf is None:
            logging.error('无 {} 的环境配置'.format(memberName))
            return
        
        env = memberConf.get('PUSH')
        if env is None:
            logging.error('无 {} 的推单配置'.format(memberName))
            return

        try:

            nowTimeStr = time.strftime('%Y-%m-%d %H:%M:%S')

            bizcontent = {
                'messageContent': messageContent,
                'messageType': messageType,
                'outgoingTime': nowTimeStr,
                'pubTime': nowTimeStr
            }

            bizcontent_str = jsonHelper.toJson(bizcontent)

            data = {
                'appkey': "a78c4fcbb8434f4096683d63b9dd19b4",
                'bizcontent': bizcontent_str,
                'contenttype': "json",
                'method': messageType,
                'outshopid': shopId,
                'outusername': memberName,
                'platid': platId,
                'token': shopToken
            }

            print(jsonHelper.toJson(data))

            # 模拟客户端登陆
            jackyunclient = jackyunClient(env.get('url'))
            jackyunclient.login(memberName, env.get('code'), env.get('user'), env.get('pwd'), False)
            jackyunclient.__loginCache = {}
            # 抓单结果推送
            rsp = jackyunclient.httpPost('/jkyun/omsapi-gate/test/openapi/do/notify', data)
            print(rsp)

            return rsp

        except Exception as e:
            ret = "消息推送失败 : " + str(e)
            logging.error(ret)


    # 推单静态方法(管家)
    @staticmethod
    def pushToOpenApi(pushUrl, appkey, platId, memberName, shopToken, orderObj, needFillUp=False):

        nowTimeStr = time.strftime('%Y-%m-%d %H:%M:%S')
        timestamp = int(time.time())

        messageContent = {
            'needFillUp' : needFillUp,
            'orders' : [orderObj]
        }

        bizObject = {
            'messageType' : 'Poly_Order_Push',
            'outgoingTime' : nowTimeStr,
            'pubTime': nowTimeStr,
            'messageContent' : jsonHelper.toJson(messageContent)
        }

        postVars = {
            'method' : 'Poly_Order_Push',
            'appkey': appkey,
            'token': shopToken,
            'platid': platId,
            'version': '1.0',
            'bizcontent' : jsonHelper.toJson(bizObject),
            'contenttype' : 'json',
            'timestamp' : timestamp
        }

        # 计算签名：暂时以appkey代替secret
        postVars['sign'] = signHelper.makeMd5Sign(appkey, postVars)
        # 加入其他参数
        postVars['contextid'] = randomMaker.makeRequestId()
        postVars['outusername'] = memberName,

        try:
            ssrequest = requests.session()
            rsp = ssrequest.post(pushUrl, data=postVars)
            if not rsp:
                rspJson = {
                    "url" : pushUrl,
                    "status_code": rsp.status_code,
                    "msg": rsp.reason
                }
                return jsonHelper.toJson(rspJson)
            return rsp.text
        except Exception as e:
            ret = "推单失败 : " + str(e)
            logging.error(ret)
