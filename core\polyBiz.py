'''
此模块包含所有对polyProcessor重写的子类
用于处理各接口的特殊业务
'''
import copy

from core.base import *
from core.repository import *
from core.order import *
from core.polyWebSpider import *


class polyProcessor:
    """
    聚合业务处理基类
    """
    def __init__(self, _method: str, postvars: dict):
        self.repo = commonExampleRepository()
        self.method = _method
        self.postvars = postvars
        self.platId = postvars.get('platid')
        self.req_biz = postvars.get('bizcontent', '')
        self.fail = postvars.get('fail', '')
        self.user = postvars.get('outusername', '')
        self.tag = postvars.get('tag', '')

   
    def isBusinessMethod(self):
        """
        是否为电商平台方法
        """
        if not self.method:
            return False
        return self.method.startswith('differ.jh.business.')

    def processBiz(self, bizContent):
        """
        供子类重写的方法
        """
        return bizContent

    def getResult(self, requestid) -> baseRespone:
        """
        获取业务处理结果
        """
        if len(self.fail) > 0 and self.method != 'differ.jh.business.batchsend':
            bizResponse = baseRespone(requestid)
            bizResponse.msg = self.fail
            bizResponse.submessage = self.fail
            return bizResponse
        
        isBusinessMethod = self.isBusinessMethod()

        page_content = ''
        # 非电商业务方法, 优先从样例库查询返回报文
        if not isBusinessMethod:
            examples = self.repo.getOneExampleByCondition(self.method, int(self.platId), self.tag)
            if examples:
                page_content = examples[0].content
        
        if not page_content:
            # 从聚合页面查询返回样例
            page_content = polyWebSpider().getApiResponseExample(self.method)

        # 返回样例查询失败
        if not page_content:
            bizResponse = baseRespone(requestid)
            bizResponse.msg = '请求接口' + self.method + '返回样例失败'
            return bizResponse
        
        # 解析
        bizResponse = jsonHelper.deJson(page_content)
        bizResponse.polyapirequestid = requestid
        
        if isBusinessMethod:
            # 根据接口对应样例赋值指定字段
            self.adjustResponseByExample(bizResponse)
            # 请求字段映射对应返回字段
            self.adjustResponseByRequest(bizResponse)

        # 转object后进行特殊处理
        bizResponse = self.processBiz(bizResponse)
        return bizResponse
    

    def adjustResponseByExample(self, bizResponse):
        """
        根据通用样例库的配置，调整返回参数的相应字段
        """
        # 查询通用样例库
        examples = self.repo.getOneExampleByCondition(self.method, int(self.platId), self.tag)
        if len(examples) == 0:
            return

        attr_dic = {}
        for example in examples:
            if not example.content or not example.biz_feild:
                continue
            # 属性名称转小写
            attr_name = example.biz_feild.lower()
            # 需要赋值的是已有属性时，跳过
            if attr_name in attr_dic:
                continue
            
            # 解析新属性值
            attr_value = jsonHelper.deJson(example.content)      
            # 属性赋值
            attr_dic.update({attr_name: attr_value}) 
        
        if not attr_dic:
            return
        
        for attr_name, new_value in attr_dic.items():
            # 需要赋值的是已有属性时，需要根据已有属性调整
            if hasattr(bizResponse, attr_name):
                old_value = getattr(bizResponse, attr_name)
                # 属性是集合，但 新属性值 不是集合时，需要转换为集合
                if isinstance(old_value, list) and isinstance(new_value, list) == False:
                    new_value = [new_value]
            # 属性赋值
            setattr(bizResponse, attr_name, new_value)


    def adjustResponseByRequest(self, bizResponse):
        """
        请求字段映射对应返回字段(仅限多对多接口)
        """
        methodCfgList = self.repo.getMethodConfig(self.method)
        if len(methodCfgList) == 0:
            return
        
        reqBizObj = jsonHelper.deJson(self.req_biz.lower())
        
        # 优惠明细接口特殊处理
        if self.method == 'differ.jh.business.getordercoupon':
            ordernos = reqBizObj.ordernos.split(',')
            reqBizObj.ordernos = list(map(lambda x: sqlResult({'orderno' : x}), ordernos))

        for configItem in methodCfgList:
            if len(configItem.req_feild) == 0 or len(configItem.return_feild) == 0:
                continue

            reqAttr = configItem.req_feild.lower()
            if hasattr(reqBizObj, reqAttr) == False:
                continue

            returnAttr = configItem.return_feild.lower()
            if hasattr(bizResponse, returnAttr) == False:
                continue

            reqList = getattr(reqBizObj, reqAttr)
            if isinstance(reqList, list) == False or len(reqList) == 0:
                continue

            resultList = getattr(bizResponse, returnAttr)
            if isinstance(resultList, list) == False or len(resultList) == 0:
                continue
            
            templateItem = resultList[0]
            resultList.clear()

            feild_map = [] if len(configItem.feild_map) == 0 else json.loads(configItem.feild_map)
            # 根据请求的集合生成返回集合
            for reqItem in reqList:
                resultItem = copy.deepcopy(templateItem)
                resultList.append(resultItem)

                # 字段值映射(不给字段赋固定值, 如有需要, 可以在[通用]界面配置返回内容)
                for item in feild_map:
                    if 'request' not in item:
                        continue
                    reqFeild = item['request'].lower()
                    if len(reqFeild) == 0 or hasattr(reqItem, reqFeild) == False:
                        continue
                    
                    # 优先取指定字段, 否则默认映射到同名字段
                    responseFeild = reqFeild
                    if 'response' in item and len(responseFeild) > 0:
                        responseFeild = item['response'].lower()

                    feildVal = getattr(reqItem, reqFeild)
                    setattr(resultItem, responseFeild, feildVal)

            # 设置总数
            setattr(bizResponse, 'numtotal', len(resultList))


class polyProcessorFactory:
    """
    聚合业务处理器工厂类
    """

    @staticmethod
    def getProcessor(method:str, postvars:dict):
        """
        根据方法名，获取并初始化对应的业务处理类
        """
        method = method.lower()
        
        # 找到method对应的子类
        cls_name = method.replace('.', '_')
        for sc in polyProcessor.__subclasses__():
            if sc.__name__ == cls_name:
                return sc(method, postvars)
            
        # 没找到对应业务子类的，使用基类处理
        return polyProcessor(method, postvars)


# 抓单
class differ_jh_business_getorder(polyProcessor):

    # 常见表情符号集合
    emojis = ["😊", "😂", "😍", "🤔", "🙃", "😎", "🥳", "😜", "🤩", "😇", "👏", "👍", "🎉"]

    # 业务特殊处理
    def processBiz(self, bizContent):

        reqBizObj = jsonHelper.deJson(self.req_biz)
        # 需要进行订单数据修正
        fix_order = True
        # 订单库查询相应平台订单
        lib_orders = orderExampleRepository().pickOrdersByPlat(self.platId, self.tag)
        if len(lib_orders) > 0:
            bizContent.orders = lib_orders
            fix_order = False

        # 遍历订单，根据请求参数处理订单
        for order in bizContent.orders:
            # 根据入参赋值
            if hasattr(reqBizObj, 'orderstatus') and reqBizObj.orderstatus and reqBizObj.orderstatus != 'JH_98':
                order.tradestatus = reqBizObj.orderstatus
            # 订单类型赋值
            if hasattr(reqBizObj, 'ordertype') and reqBizObj.ordertype:
                order.ordertype = reqBizObj.ordertype
            # 订单类型赋值
            if hasattr(reqBizObj, 'shoptype') and reqBizObj.shoptype:
                order.shoptype = reqBizObj.shoptype

            # 基础数据修正
            orderUtils.baseProcess(order, self.platId, True)

            # 由聚合接口样例值返回的订单，需要做数据修正
            if fix_order:
                self.fixMockOrder(order, reqBizObj)

            # 特殊平台赋值        
            self.processPlatBiz(order, reqBizObj)

        # 有单号，说明查的是订单详情
        if hasattr(reqBizObj, 'platorderno') and len(reqBizObj.platorderno) > 0:
            if len(bizContent.orders) > 1:
                bizContent.orders = bizContent.orders[:1]
            bizContent.orders[0].platorderno = reqBizObj.platorderno
            if fix_order:
                bizContent.orders[0].tradestatus = 'JH_02'

        return bizContent

    # mock数据修正
    def fixMockOrder(self, order, reqBizObj):
        order.sendtype = 'JH_Other'
        order.ispresaleorder = '0'
        order.fenxiaoorderno = ''
        order.resellerid = ''
        order.isdaixiao = False
        order.isship = False
        order.ishwgflag = False
        order.authenticateflag = 1
        order.incrementmodifytime = None

        # 商品级赋值
        for goods in order.goodinfos:
            goods.refundstatus = 'JH_07'
            goods.fenxiaotaxtotal = '0'
            goods.goodscount = 2
            goods.goodscount2 = '0'
            goods.ispresale = '0'
            goods.isgift = '0'
            goods.ishwgflag = '0'
            goods.authenticateflag = 1
        # 多加一个商品
        if len(order.goodinfos) == 1:
            d = copy.deepcopy(order.goodinfos[0])
            d.suborderno = randomMaker.makeRandom(12)
            d.tradegoodsno = randomMaker.makeRandom(10)
            d.productid = randomMaker.makeRandom(11)
            d.outitemid = d.tradegoodsno
            order.goodinfos.append(d)
            # 商品件数修正
            order.productnum = len(order.goodinfos)

    def add_emoji_to_random_str_field(self, obj):
        """
        随机选择一个字符串类型的属性，并在其值末尾添加一个表情符号
        
        参数:
            obj: 任意Python对象(类实例)
            
        返回:
            修改后的对象(原对象被直接修改)
        """
        
        # 收集所有字符串类型的实例属性
        str_fields = []
        for attr_name in dir(obj):
            # 跳过特殊方法和非实例属性
            if attr_name.startswith("__") and attr_name.endswith("__"):
                continue
                
            try:
                attr_value = getattr(obj, attr_name)
                # 检查是否为字符串且可写(避免方法)
                if isinstance(attr_value, str) and not callable(attr_value):
                    str_fields.append(attr_name)
            except AttributeError:
                continue
        
        # 如果有可用的字符串字段
        if str_fields:
            # 随机选择一个字段
            chosen_field = random.choice(str_fields)
            # 获取原始值并添加随机表情
            original_value = getattr(obj, chosen_field)
            new_value = original_value + random.choice(self.emojis)
            # 更新对象属性
            setattr(obj, chosen_field, new_value)
            print(f"Added emoji to field: {chosen_field}")
        else:
            print("No string fields found in the object")
        
        return obj

    # 平台特殊处理
    def processPlatBiz(self, order, reqBizObj):

        if self.platId == '1034':
            order.oaid = "AARspY4tE+10aj0we4DYKpibCe/eFee931tqLQNqczJ/kSQwzJyL2j66rxwKvn8jMTo="
            order.suppliername = "笛佛Supplier"

        if self.platId == '1128' and len(reqBizObj.sipshopid) > 0:
            order.shopid = reqBizObj.sipshopid

        if self.platId == '1008':
            order.ispresaleorder = True
            if order.tradestatus == 'JH_01':
                order.presalestatus = 'JH_DEPOSIT_NOBALANCE'
            elif order.tradestatus == 'JH_02':
                order.presalestatus = 'JH_DEPOSIT_BALANCE'

        return


# 批量发货
class differ_jh_business_batchsend(polyProcessor):

    def processBiz(self, bizContent):

        if self.platId == '60' and self.req_biz.find('JH_02') > 0:
            self.fail = '亚马逊确认发货结果失败'

        if len(self.fail) > 0:
            # 外层赋值
            bizContent.code = '40000'
            bizContent.issuccess = '0'
            bizContent.msg = 'Complex Error'
            bizContent.subcode = 'LXO.REQUEST_FAILURE.BATCHERROR'
            bizContent.submessage = '调用批量接口出错:' + self.fail

        # 根据请求参数生成返回
        for orderResult in bizContent.results:           
            orderResult.deliveryid = randomMaker.makeRandom(10)
            if len(self.fail) > 0:
                orderResult.code = '40000'
                orderResult.issuccess = '0'
                orderResult.message = self.fail
                orderResult.submessage = self.fail

        bizContent.sendrequestid = bizContent.polyapirequestid
        return bizContent


# 下载商品
class differ_jh_business_downloadproduct(polyProcessor):

    def processBiz(self, bizContent):

        # 查询样例商品
        if len(self.user) == 0:
            return bizContent
        
        bizContent.fileurls = []
        bizContent.requestid = bizContent.polyapirequestid

        repo = goodsRepository()

        # 查询样例商品
        bizContent.goodslist, bizContent.totalcount = repo.getGoods({'plat': self.platId}, True)

        # 入参解析
        reqBizObj = jsonHelper.deJson(self.req_biz.lower())

        # 子店铺id赋值
        if hasattr(reqBizObj, "sipshopid"):
            for item in bizContent.goodslist:
                item.shopid = reqBizObj.sipshopid

        # 下一页参数赋值
        if hasattr(reqBizObj, 'pageindex') and hasattr(reqBizObj, 'pagesize') and int(reqBizObj.pageindex) * int(reqBizObj.pagesize) < bizContent.totalcount:
            bizContent.ishasnextpage = True
        else:
            bizContent.nexttoken = ''
            bizContent.ishasnextpage = False

        return bizContent


# 修改店铺信息
class differ_jh_updateshopinfo(polyProcessor):

    def processBiz(self, bizContent):
        bizContent.shopid = randomMaker.makeRandom(10)
        return bizContent


# # 退货退款单下载
# class differ_jh_business_getrefund(polyProcessor):
    
#     def processBiz(self, bizContent):

#         for order in bizContent.refunds:
#             order.suborderid = []
#             order.pictures = []
#             order.suborderidmap = None
#             order.refundtype = ''
#             order.refundno = randomMaker.makeRandomNo()
#             order.platorderno = randomMaker.makeRandomNo()

#         for order in bizContent.exchanges:
#             order.exchangeorderno = randomMaker.makeRandomNo()
#             order.platorderno = randomMaker.makeRandomNo()

#         return bizContent

# 查询网点信息
class differ_jh_business_getwebsite(polyProcessor):

    def processBiz(self, bizContent):
        bizContent.numtotal = len(bizContent.sites)
        return bizContent
    
# 查询商家仓库
class differ_jh_business_querywarehouse(polyProcessor):

    def processBiz(self, bizContent):
        bizContent.totalcount = len(bizContent.warehouseinfos)
        return bizContent
    
# # 生成平台授权网址
# class differ_jh_buildeauthorizeurl(polyProcessor):

#     def processBiz(self, bizContent):
#         shopId = self.postvars.get('outshopid', self.postvars.get('shopid'))
#         bizContent.authorizeurl = '{}&selling_partner_id={}&shopId={}'.format(bizContent.authorizeurl, shopId, shopId)
#         if 'code=' not in bizContent.authorizeurl:
#             # 取随机字母
#             random_code = ''.join(random.sample(string.ascii_letters + string.digits, 32))
#             bizContent.authorizeurl += '&code=' + random_code
#         return bizContent

    
# # 获取平台SessionKey
# class differ_jh_getauthorizesessionkey(polyProcessor):
 
#     def processBiz(self, bizContent):
#         mytime = datetime.date.today()
#         bizContent.sessionkeyexpiretime = (mytime + datetime.timedelta(days = 30)).strftime('%Y-%m-%d %H:%M:%S')
#         bizContent.refreshtokenexpiretime = (mytime + datetime.timedelta(days = 180)).strftime('%Y-%m-%d %H:%M:%S')
#         bizContent.subscriptionexpiretime = (mytime + datetime.timedelta(days = 200)).strftime('%Y-%m-%d %H:%M:%S')
#         bizContent.attrjson = ''
#         bizContent.userid = self.postvars.get('outusername')
#         bizContent.venderid = self.postvars.get('outshopid', self.postvars.get('shopid'))
#         return bizContent