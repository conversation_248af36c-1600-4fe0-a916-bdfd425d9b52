import requests
import urllib
import logging

from bs4 import BeautifulSoup
from core.base import *
from utils.jsonHelper import *


# polyapi接口页爬虫

class polyWebSpider:

    jhHost = "http://**************:2233"

    def getApiAll(self):

        url = self.jhHost + '/website/doc/getAllApi'
        # 超时时间设置3s
        f = urllib.request.urlopen(url, timeout=3)

        data = f.read().decode('utf-8')
        dataObj = jsonHelper.deJson(data)

        return dataObj.result.data

    def getApiRequestExample(self, method):

        url = self.jhHost + '/website/doc/getApiDoc?method=' + method
        try:
            # 超时时间设置3s
            f = urllib.request.urlopen(url, timeout=3)

            data = f.read().decode('utf-8')
            dataObj = jsonHelper.deJson(data)
            data = self.buildObjFromAttr(dataObj.result.data.request)

            return jsonHelper.toJson(data)
        except:
            return self.getContentFromNetVersion(method)

    def getApiResponseExample(self, method):

        url = self.jhHost + '/website/doc/getApiDoc?method=' + method
        try:
            # 超时时间设置3s
            f = urllib.request.urlopen(url, timeout=3)

            data = f.read().decode('utf-8')
            dataObj = jsonHelper.deJson(data)
            data = self.buildObjFromAttr(dataObj.result.data.response)
            data.code = '10000'
            data.msg = 'SUCCESS'

            return jsonHelper.toJson(data)
        except:
            return self.getContentFromNetVersion(method)
        

    def getContentFromNetVersion(self, method):

        url = 'http://**************:8088/Api/Index?method=' + method

        # 浏览器伪装
        head = {
            "User-Agent": "Mozilla / 5.0(Windows NT 10.0; Win64; x64) AppleWebKit / 537.36(KHTML, like Gecko) Chrome / 80.0.3987.122  Safari / 537.36",
            "X-Requested-With": "XMLHttpRequest"
        }
        # GET请求
        strhtml = requests.get(url, headers=head)
        soup = BeautifulSoup(strhtml.text,  'lxml')
        data = soup.select('#SuccessJson')

        if len(data) == 0:
            return ''
        return data[0].text

    def buildDicFromAttr(self, attrInfo):

        attrDic = {}
        for item in attrInfo:
            name = item.name.lower()
            attrDic[name] = item.demoText
            if item.type == "List":
                attrDic[name] = []
                attrDic[name].append(self.buildDicFromAttr(item.children))

        return attrDic

    def buildObjFromAttr(self, attrInfo):

        def obj(): return None

        for item in attrInfo:
            name = item.name.lower()
            if item.type == "List":
                subObj = []
                subObj.append(self.buildObjFromAttr(item.children))
                setattr(obj, name, subObj)
            elif item.type == "Object":
                subObj = self.buildObjFromAttr(item.children)
                setattr(obj, name, subObj)
            else:
                setattr(obj, name, self.getObjValue(item))

        return obj

    def getObjValue(self, objInfo):

        objInfo.demoText = objInfo.demotext.strip()

        try:

            if objInfo.type == "BigDecimal" or objInfo.type == "Double" or objInfo.type == "Float":
                return float(objInfo.demoText.replace('元', ''))

            # if objInfo.type == "Integer" or objInfo.type == "int":
            #     return int(objInfo.demoText)

            if objInfo.type == "boolean":
                return objInfo.demoText == 'true'

        except Exception as e:
            logging.error('数据类型转化出错：' + str(e))

        return objInfo.demoText

