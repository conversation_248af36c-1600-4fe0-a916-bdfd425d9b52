from collections import defaultdict
import logging

from core.base import *
from utils.jsonHelper import *
from utils.mysqlHelper import *
from pymysql.converters import escape_string


# 请求记录仓储
class recordRepository(baseRepository):

    # 插入
    def insert(self, reqId, platId, user, token, method, req, bak):
        sql = "insert into glo_poly_simulator_record(request_id,plat,user_name,token,method) values({},{},'{}','{}','{}');".format(
            reqId, platId, user, token, method)

        # print(sql)
        # 执行插入
        mysqlHelper.doExecute(sql, self.db)

        # 插入详情
        sql = "insert into glo_poly_simulator_record_detail(request_id, bill_type, bill_content) values('{}',{},'{}'), ('{}',{},'{}');".format(
            reqId, 0, escape_string(req), reqId, 1, escape_string(bak))

        # 执行插入
        mysqlHelper.doExecute(sql, self.db)

    # 按条件查询
    def getRecord(self, reqVars):

        # 查询条件
        condition = ''
        if 'request_id' in reqVars.keys() and reqVars['request_id']:
            condition = " request_id = '" + reqVars['request_id'] + "'"

        else:
            if 'plat' in reqVars.keys() and reqVars['plat']:
                condition += ' plat = ' + reqVars['plat']
            if 'api_type' in reqVars.keys() and reqVars['api_type']:
                if len(condition) > 0:
                    condition += ' and '
                condition += " method = '" + reqVars['api_type'] + "'"
            if 'user_name' in reqVars.keys() and reqVars['user_name']:
                if len(condition) > 0:
                    condition += ' and '
                condition += " user_name = '" + reqVars['user_name'] + "'"
            if 'time_start' in reqVars.keys() and reqVars['time_start']:
                if len(condition) > 0:
                    condition += ' and '
                condition += " req_time >= '" + reqVars['time_start'] + "'"
            if 'time_end' in reqVars.keys() and reqVars['time_end']:
                if len(condition) > 0:
                    condition += ' and '
                condition += " req_time <= '" + reqVars['time_end'] + "'"

        queryCount = False
        orderField = ' order by id desc '
        if len(condition) == 0 or 'request_id' not in condition:
            queryCount = True
            orderField = self.getOrderAndLimit(reqVars)

        return mysqlHelper.query('*', 'glo_poly_simulator_record', condition, orderField, queryCount, self.db)

    # 查询请求详情
    def getRecordDetail(self, reqId):

        detailObj = {}
        detailObj['success'] = False

        if len(reqId) == 0:
            detailObj['msg'] = 'reqId cannot empty'
            return detailObj
        
        sql = "select * from glo_poly_simulator_record_detail where request_id = '{}';".format(reqId)

        ret = mysqlHelper.selectList(sql, self.db)

        detailObj['request']= None
        detailObj['returnInfo'] = None
        detailObj['msg'] = '详情信息查询失败'

        for item in ret:
            if item.bill_type == 0:
                detailObj['success'] = True
                detailObj['request'] = item.bill_content
                continue
            if item.bill_type == 1:
                detailObj['success'] = True
                detailObj['returnInfo'] = item.bill_content
                continue
        
        return detailObj

    def getRecordsByMaxtimeId(self, maxTime, maxCount):
        condition = " req_time <= '{}'".format(str(maxTime))
        orderField = 'limit {}'.format(str(maxCount))
        return mysqlHelper.query('request_id', 'glo_poly_simulator_record', condition, orderField, False, self.db)
    
    def delRecordsByRequestId(self, request_id_list):
        if not request_id_list:
            return        
        sql = 'delete from glo_poly_simulator_record where request_id in ({})'.format(','.join(list(map(lambda x: "'{}'".format(x), request_id_list))))
        return mysqlHelper.doExecute(sql, self.db)    
        
    def delRecordsDetailByRequestId(self, request_id_list):
        if not request_id_list:
            return        
        sql = 'delete from glo_poly_simulator_record_detail where request_id in ({})'.format(','.join(list(map(lambda x: "'{}'".format(x), request_id_list))))
        return mysqlHelper.doExecute(sql, self.db)

# 订单样例仓储
class orderExampleRepository(baseRepository):

    #插入
    def insert(self, platId, content, tag, priority, ip):
        sql = "insert into glo_poly_simulator_order_example(plat,content,tag,priority, author_ip) values({},'{}','{}',{}, '{}');".format(
            platId, escape_string(content.replace('\xa0', ' ')), escape_string(tag), priority, ip)
        return mysqlHelper.doExecute(sql, self.db)

    #更新
    def update(self, id, content, tag, priority, ip):
        sql = "update glo_poly_simulator_order_example set content='{}', tag='{}', priority={}, author_ip='{}' where id={} ;".format(
            escape_string(content.replace('\xa0', ' ')), escape_string(tag), priority, ip, id)
        return mysqlHelper.doExecute(sql, self.db)

    # 查询单个订单
    def getOne(self, id):
        sql = 'select content from glo_poly_simulator_order_example where id = {} limit 1'.format(
            id)
        try:
            cursor = self.db.cursor()
            ret = cursor.execute(sql)
            if ret == 0:
                return ''
            ret = cursor.fetchone()[0]
            cursor.close()
            return ret
        except Exception as e:
            logging.error('查询单个订单出错：' + str(e))
            return ''

    # 查询单个订单记录
    def getOneDetail(self, id):
        sql = 'select * from glo_poly_simulator_order_example where id = {} limit 1'.format(id)
        return mysqlHelper.selectList(sql, self.db)

    # 检查对应数据的ip是否相同
    def checkIpSame(self, id, ip):
        params = "id = {} and author_ip='{}'".format(id, ip)
        return mysqlHelper.queryExist('glo_poly_simulator_order_example', params, self.db)

    # 查询
    def query(self, reqVars):

        # 查询条件
        condition = ''
        if reqVars and 'plat' in reqVars.keys() and reqVars['plat']:
            condition += ' plat = ' + reqVars['plat']

        return mysqlHelper.query('*', 'glo_poly_simulator_order_example', condition, self.getOrderAndLimit(reqVars), True, self.db)

    #删除
    def delete(self, id):
        sql = "delete from glo_poly_simulator_order_example where id={} ;".format(
            id)
        return mysqlHelper.doExecute(sql, self.db)

    # 按平台取最多10条订单数据
    def pickOrdersByPlat(self, plat, tag):
        # 查询数据
        sql = 'select * from glo_poly_simulator_order_example where plat = ' + plat
        if len(tag) > 0:
            sql += " and tag = '" + tag + "' "
        sql += ' order by priority desc limit 5'

        ret = mysqlHelper.selectList(sql, self.db)

        # json数据转obj
        orders = []
        for item in ret:
            if hasattr(item, "content") == False or len(item.content) < 2:
                continue
            try:
                order = jsonHelper.deJson(item.content.replace('\xa0', ' '))
                if order != None and hasattr(order, 'platorderno') and len(order.platorderno) > 0:
                    orders.append(order)
            except Exception as e:
                logging.error('订单数据转json失败：' + str(e))

        return orders

# 商品仓储
class goodsRepository(baseRepository):

    #插入
    def insert(self, platId, content, tag, ip):
        if not platId:
            platId = 0
        sql = "insert into glo_poly_simulator_goods_example(plat,content,tag,author_ip) values({},'{}','{}','{}');".format(platId, escape_string(content.replace('\xa0', ' ')), escape_string(tag), ip)
        return mysqlHelper.doExecute(sql, self.db)

    #更新
    def update(self, id, content, tag, ip):
        sql = "update glo_poly_simulator_goods_example set content='{}', tag='{}', author_ip='{}' where id={} ;".format(
            escape_string(content.replace('\xa0', ' ')), escape_string(tag), ip, id)
        return mysqlHelper.doExecute(sql, self.db)

    # 删除
    def delGoods(self, id):
        sql = "delete from glo_poly_simulator_goods_example where id={} ;".format(id)
        return mysqlHelper.doExecute(sql, self.db)
    
    # 查询单个商品
    def getOne(self, id):
        sql = 'select * from glo_poly_simulator_goods_example where id = {} limit 1'.format(id)
        return mysqlHelper.selectList(sql, self.db)
    
    # 查询商品
    def getGoods(self, reqVars, resolve):

        feilds = '*' if resolve else 'id,plat,content,tag,author_ip,gmt_modify'

        # 查询条件
        condition_arr = []    
        if len(reqVars) > 0:
            plat_val = 0
            if 'plat' in reqVars.keys() and reqVars['plat']:
                plat_val = reqVars['plat']
                if isinstance(plat_val, str):
                    plat_val = int(plat_val)
            
            tag = ''
            if 'tag' in reqVars.keys() and reqVars['tag']:
                tag = reqVars['tag']
        
            if plat_val:
                if tag:
                    condition_arr.append("((plat = {} AND tag = '{}') OR plat = 0)".format(plat_val, tag))
                else:
                    condition_arr.append("plat in (0, {})".format(plat_val))

        condition = ' and '.join(condition_arr) if len(condition_arr) > 0 else ''
        
        ret, count = mysqlHelper.query(feilds, 'glo_poly_simulator_goods_example', condition, self.getOrderAndLimit(reqVars), True, self.db)
        if resolve == False:
            return ret, count

        goodsList = []
        for item in ret:
            if hasattr(item, "content") == False or len(item.content) < 2:
                continue
            try:
                # json数据转obj
                goods = jsonHelper.deJson(item.content.replace('\xa0', ' '))
                # 商品合法性校验
                if goods != None and hasattr(goods, 'platproductid') and len(goods.platproductid) > 0:
                    goodsList.append(goods)
            except Exception as e:
                logging.error('商品数据转json失败: ' + str(e))

        return goodsList, count

# 店铺仓储
class shopRepository(baseRepository):

    # 根据平台值查询店铺
    def getShopByPlat(self, platId, auth_shop_only):
        shopList = []
        try:
            if self.db is None:
                return shopList
            sql = 'select shop_id,shop_name,shop_type,poly_token from j_api_shop_config where shop_type = {}'.format(
                platId)
            if auth_shop_only:
                sql += ' and auth_status = 0 '
            shopList = mysqlHelper.selectList(sql, self.db)
        except Exception as e:
            logging.error('根据平台值查询店铺出错：' + str(e))

        return shopList

# 平台仓储
class platRepository(baseRepository):

    # 获取平列表
    def getPlatList(self):
        lst = []
        try:
            if self.db is None:
                return lst
            sql = 'select PlatValue,Name from dev_plat where PlatType = 0 and APIPlatType = 3'
            lst = mysqlHelper.selectList(sql, self.db)
        except Exception as e:
            logging.error('查询平台出错：' + str(e))
        
        return lst

    # 新增平台
    def addPlat(self, platId, platName):
        if len(platName) == 0:
            return '平台名称不能为空'
        sql = "insert IGNORE into dev_plat(PlatValue,Name,PlatType,APIPlatType,CreateTime,LastModifyTime) values ({},'{}',0,3,NOW(),NOW())".format(platId, platName)
        return mysqlHelper.doExecute(sql, self.db)
    
# 多态方法配置仓储
class polyMethodConfigRepository(baseRepository):

    # 插入配置
    def insert(self, method, req_feild, return_feild, feild_map, ip):
        sql = "insert into glo_poly_simulator_poly_method_config(method,req_feild,return_feild,feild_map,author_ip) values('{}','{}','{}','{}','{}');".format(
            method, req_feild, return_feild, escape_string(feild_map) if feild_map else '', ip)
        return mysqlHelper.doExecute(sql, self.db)

    # 更新配置
    def update(self, id, feild_map, ip):
        sql = "update glo_poly_simulator_poly_method_config set feild_map='{}', author_ip='{}' where id={};".format(
            escape_string(feild_map) if feild_map else '', ip, id)
        return mysqlHelper.doExecute(sql, self.db)

    # 删除配置
    def delete(self, id):
        sql = "delete from glo_poly_simulator_poly_method_config where id={};".format(id)
        return mysqlHelper.doExecute(sql, self.db)

    # 查询单个配置
    def getOne(self, id):
        sql = 'select * from glo_poly_simulator_poly_method_config where id = {} limit 1'.format(id)
        return mysqlHelper.selectList(sql, self.db)

    # 根据方法名查询配置
    def getByMethod(self, method_name):
        lst = []
        try:
            if self.db is None:
                return lst
            sql = "select * from glo_poly_simulator_poly_method_config where method = '{}'".format(method_name)
            lst = mysqlHelper.selectList(sql, self.db)
        except Exception as e:
            logging.error('根据方法名查询配置出错：' + str(e))
        return lst

    # 根据方法名和字段查询配置
    def getByMethodAndFields(self, method_name, req_feild, return_feild):
        lst = []
        try:
            if self.db is None:
                return lst
            sql = "select * from glo_poly_simulator_poly_method_config where method = '{}' and req_feild = '{}' and return_feild = '{}'".format(
                method_name, req_feild, return_feild)
            lst = mysqlHelper.selectList(sql, self.db)
        except Exception as e:
            logging.error('根据方法名和字段查询配置出错：' + str(e))
        return lst

    # 根据方法名和返回字段查询配置（用于mappingId查询）
    def getByMethodAndReturnField(self, method_name, return_feild):
        lst = []
        try:
            if self.db is None:
                return lst
            sql = "select * from glo_poly_simulator_poly_method_config where method = '{}' and return_feild = '{}' limit 1".format(
                method_name, return_feild)
            lst = mysqlHelper.selectList(sql, self.db)
        except Exception as e:
            logging.error('根据方法名和返回字段查询配置出错：' + str(e))
        return lst


# 通用样例仓储
class commonExampleRepository(baseRepository):

    def getMethodConfig(self, method_name):
        lst = []
        try:
            if self.db is None:
                return lst
            sql = "select * from glo_poly_simulator_poly_method_config where method = '{}' ".format(method_name)

            lst = mysqlHelper.selectList(sql, self.db)
        except Exception as e:
            logging.error('查询接口配置出错：' + str(e))
        
        return lst   

    # 根据条件查询样例
    def getOneExampleByCondition(self, method_name, platId, tag):
        lst = []
        try:
            if self.db is None:
                return lst
            sql = "select biz_feild,content,plat from glo_poly_simulator_example where method = '{}' ".format(method_name)
            if tag:
                sql += " and ((plat = {} AND tag = '{}') OR plat = 0)".format(platId, tag)
            else:
                sql += " and plat in (0, {})".format(platId)
            sql += " order by plat desc, gmt_modify desc"

            lst = mysqlHelper.selectList(sql, self.db)

            if lst and len(lst) > 1:
                # 使用 defaultdict 进行分组
                groups = defaultdict(list)
                for obj in lst:
                    groups[obj.biz_feild].append(obj)

                # 提取每组的第一个元素
                lst = [group[0] for group in groups.values()]

            return lst

        except Exception as e:
            logging.error('根据条件查询样例出错：' + str(e))
        
        return lst

    # 查询单个样例
    def getExamples(self, reqVars):
        lst = []
        try:
            if self.db is None:
                return lst, 0            
            # 查询条件
            condition_arr = []    
            if len(reqVars) > 0:
                if 'method' in reqVars.keys() and reqVars['method']:
                    condition_arr.append("method = '{}'".format(reqVars['method']))
                if 'plat' in reqVars.keys() and reqVars['plat']:
                    plat_val = reqVars['plat']
                    if isinstance(plat_val, str):
                        plat_val = int(plat_val)
                    if plat_val > 0:
                        condition_arr.append('plat = 0 or plat = ' + str(plat_val)) 
                if 'tag' in reqVars.keys() and reqVars['tag']:
                    condition_arr.append("tag = '{}'".format(reqVars['tag']))
            
            condition = ' and '.join(condition_arr) if len(condition_arr) > 0 else ''

            return mysqlHelper().query('*', 'glo_poly_simulator_example', condition, self.getOrderAndLimit(reqVars), True, self.db)
        except Exception as e:
            logging.error('查询单个样例出错：' + str(e))
        
    # 查询单个样例
    def getOne(self, id):
        sql = 'select * from glo_poly_simulator_example where id = {} limit 1'.format(id)
        return mysqlHelper.selectList(sql, self.db)

    # 删除
    def delete(self, id):
        sql = "delete from glo_poly_simulator_example where id={} ;".format(id)
        return mysqlHelper.doExecute(sql, self.db)

    # 插入
    def insert(self, platId, method, biz_feild, content, tag, ip):
        sql = "insert into glo_poly_simulator_example(plat,method,biz_feild,content,tag,author_ip) values({},'{}','{}','{}','{}','{}');".format(
            platId, method.lower(), biz_feild, escape_string(content.replace('\xa0', ' ')), escape_string(tag), ip)
        return mysqlHelper.doExecute(sql, self.db)

    # 更新
    def update(self, id, platId, biz_feild, content, tag, ip):
        sql = "update glo_poly_simulator_example set plat={}, biz_feild='{}', content='{}', tag='{}', author_ip='{}' where id={} ;".format(
            platId, biz_feild, escape_string(content.replace('\xa0', ' ')), escape_string(tag), ip, id)
        return mysqlHelper.doExecute(sql, self.db)
    