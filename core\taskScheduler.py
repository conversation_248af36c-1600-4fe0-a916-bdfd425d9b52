import datetime
import logging
import sys

# 兼容 Python 3.8 及以下版本
if sys.version_info >= (3, 9):
    from zoneinfo import ZoneInfo
else:
    try:
        from backports.zoneinfo import ZoneInfo
    except ImportError:
        # 如果没有 backports.zoneinfo，使用 UTC 时区
        from datetime import timezone
        ZoneInfo = lambda name: timezone.utc if name == 'UTC' else timezone(datetime.timedelta(hours=8))

from core.service import recordService
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger


# 创建调度器，明确指定时区以避免 pytz 警告
scheduler = BackgroundScheduler(timezone=ZoneInfo('Asia/Shanghai'))
recordSrv = recordService()
 
# 定义定时任务函数
def clear_expire_records():
    """删除过期记录的定时任务"""
    try:
        # 删除3天前的数据
        deadline = datetime.datetime.today().date() - datetime.timedelta(days=2)

        delCount = 0
        for i in range(1000):
            request_id_list, count = recordSrv.getRecordsByMaxtimeId(deadline, 200)
            if not request_id_list:
                break
            request_id_list = list(map(lambda x: x.request_id, request_id_list))
            recordSrv.delRecordsByRequestId(request_id_list)
            recordSrv.delRecordsDetailByRequestId(request_id_list)
            delCount += len(request_id_list)

        logging.info('删除数据总数: {}'.format(str(delCount)))
    except Exception as e:
        logging.error(f'清理过期记录任务执行失败: {str(e)}')

# 添加定时任务到调度器
scheduler.add_job(
    func=clear_expire_records,
    trigger=CronTrigger(hour=1, minute=0, timezone=ZoneInfo('Asia/Shanghai')),  # 每天凌晨1点执行（中国时区）
    id='clear_expire_records',
    name='清理过期记录',
    replace_existing=True
)
