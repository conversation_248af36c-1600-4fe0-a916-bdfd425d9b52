# Gunicorn配置文件
import multiprocessing
import os

# 服务器socket
bind = "0.0.0.0:9083"
backlog = 2048

# Worker进程
workers = 5  # 对应原uwsgi配置的workers=5
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 50
preload_app = True
timeout = 30  # 对应原uwsgi配置的harakiri=30
keepalive = 2

# 日志
accesslog = "./logs/gunicorn_access.log"
errorlog = "./logs/gunicorn_error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 进程命名
proc_name = 'simulator-python-web'

# 守护进程
daemon = False
pidfile = './logs/gunicorn.pid'
user = None
group = None
tmp_upload_dir = None

# SSL (如果需要)
# keyfile = None
# certfile = None

# 重启
max_requests = 1000
max_requests_jitter = 100
preload_app = True

# 其他设置
forwarded_allow_ips = '*'
secure_scheme_headers = {
    'X-FORWARDED-PROTOCOL': 'ssl',
    'X-FORWARDED-PROTO': 'https',
    'X-FORWARDED-SSL': 'on'
}
