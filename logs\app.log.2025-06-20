2025-06-20 17:23:56 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-20 17:23:57 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-20 17:23:57 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-20 17:23:57 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-20 17:23:57 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-20 17:23:57 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-20 17:23:57 | INFO  | base.py        :start                | Scheduler started
2025-06-20 17:23:57 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-20 17:23:57 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-20 17:24:28 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css -- (from=192.168.5.235)
2025-06-20 17:24:28 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js -- (from=192.168.5.235)
2025-06-20 17:24:28 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js -- (from=192.168.5.235)
2025-06-20 17:24:28 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css? -- 304 (from=192.168.5.235,2025-06-20 17:24:28) - 0.069s
2025-06-20 17:24:28 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js? -- 304 (from=192.168.5.235,2025-06-20 17:24:28) - 0.067s
2025-06-20 17:24:28 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js? -- 200 (from=192.168.5.235,2025-06-20 17:24:28) - 0.067s
2025-06-20 17:24:28 | INFO  | main.py        :log_requests         | [GET]/plat/getall -- (from=192.168.5.235)
2025-06-20 17:24:28 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=192.168.5.235,2025-06-20 17:24:28) - 0.009s
2025-06-20 17:24:28 | INFO  | main.py        :log_requests         | [GET]/common/getapi -- (from=192.168.5.235)
2025-06-20 17:24:28 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=192.168.5.235,2025-06-20 17:24:28) - 0.012s
2025-06-20 17:24:28 | INFO  | main.py        :log_requests         | [POST]/order/get -- (from=192.168.5.235)
2025-06-20 17:24:28 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=192.168.5.235,2025-06-20 17:24:28) - 0.014s
2025-06-20 17:24:28 | INFO  | main.py        :log_requests         | [POST]/order/getById -- (from=192.168.5.235)
2025-06-20 17:24:28 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=192.168.5.235,2025-06-20 17:24:28) - 0.002s
2025-06-20 17:24:29 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-20 17:24:29 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-20 17:24:29) - 0.005s
2025-06-20 17:24:29 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-20 17:24:29 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-20 17:24:29) - 0.002s
2025-06-20 17:24:31 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-20 17:24:31 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-20 17:24:31) - 0.005s
2025-06-20 17:24:36 | INFO  | main.py        :log_requests         | [POST]/common/addFieldMapping -- (from=192.168.5.235)
2025-06-20 17:24:36 | INFO  | main.py        :log_requests         | [POST]/common/addFieldMapping? -- 200 (from=192.168.5.235,2025-06-20 17:24:36) - 0.014s
2025-06-20 17:24:36 | INFO  | main.py        :log_requests         | [POST]/common/getFieldMappingBreif -- (from=192.168.5.235)
2025-06-20 17:24:36 | INFO  | main.py        :log_requests         | [POST]/common/getFieldMappingBreif? -- 200 (from=192.168.5.235,2025-06-20 17:24:36) - 0.004s
2025-06-20 17:24:41 | INFO  | main.py        :log_requests         | [POST]/common/getFieldMapping -- (from=192.168.5.235)
2025-06-20 17:24:41 | INFO  | main.py        :log_requests         | [POST]/common/getFieldMapping? -- 200 (from=192.168.5.235,2025-06-20 17:24:41) - 0.003s
2025-06-20 17:24:43 | INFO  | main.py        :log_requests         | [POST]/common/delFieldMapping -- (from=192.168.5.235)
2025-06-20 17:24:43 | INFO  | main.py        :log_requests         | [POST]/common/delFieldMapping? -- 200 (from=192.168.5.235,2025-06-20 17:24:43) - 0.016s
2025-06-20 17:24:43 | INFO  | main.py        :log_requests         | [POST]/common/getFieldMappingBreif -- (from=192.168.5.235)
2025-06-20 17:24:43 | INFO  | main.py        :log_requests         | [POST]/common/getFieldMappingBreif? -- 200 (from=192.168.5.235,2025-06-20 17:24:43) - 0.003s
2025-06-20 17:24:57 | INFO  | main.py        :log_requests         | [POST]/common/addFieldMapping -- (from=192.168.5.235)
2025-06-20 17:24:57 | INFO  | main.py        :log_requests         | [POST]/common/addFieldMapping? -- 200 (from=192.168.5.235,2025-06-20 17:24:57) - 0.052s
2025-06-20 17:24:57 | INFO  | main.py        :log_requests         | [POST]/common/getFieldMappingBreif -- (from=192.168.5.235)
2025-06-20 17:24:57 | INFO  | main.py        :log_requests         | [POST]/common/getFieldMappingBreif? -- 200 (from=192.168.5.235,2025-06-20 17:24:57) - 0.003s
2025-06-20 17:25:02 | INFO  | main.py        :log_requests         | [POST]/common/getFieldMapping -- (from=192.168.5.235)
2025-06-20 17:25:02 | INFO  | main.py        :log_requests         | [POST]/common/getFieldMapping? -- 200 (from=192.168.5.235,2025-06-20 17:25:02) - 0.003s
2025-06-20 17:25:05 | INFO  | main.py        :log_requests         | [POST]/common/updateFieldMapping -- (from=192.168.5.235)
2025-06-20 17:25:05 | ERROR | service.py     :updateFieldMapping   | 更新字段映射配置出错：polyMethodConfigRepository.update() takes 4 positional arguments but 7 were given
2025-06-20 17:25:05 | INFO  | main.py        :log_requests         | [POST]/common/updateFieldMapping? -- 200 (from=192.168.5.235,2025-06-20 17:25:05) - 0.004s
2025-06-20 17:26:18 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-20 17:26:18 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-20 17:26:18 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-20 17:26:18 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-20 17:26:20 | INFO  | main.py        :lifespan             | FastAPI应用启动中...
2025-06-20 17:26:20 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-20 17:26:20 | INFO  | base.py        :start                | Scheduler started
2025-06-20 17:26:20 | INFO  | main.py        :lifespan             | 定时任务已启动
2025-06-20 17:26:20 | INFO  | main.py        :lifespan             | FastAPI应用启动完成
2025-06-20 17:26:24 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-20 17:26:24 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-20 17:26:24 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-20 17:26:24 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-20 17:27:36 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-20 17:27:37 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-20 17:27:37 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-20 17:27:37 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-20 17:27:39 | INFO  | main.py        :lifespan             | FastAPI应用启动中...
2025-06-20 17:27:39 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-20 17:27:39 | INFO  | base.py        :start                | Scheduler started
2025-06-20 17:27:39 | INFO  | main.py        :lifespan             | 定时任务已启动
2025-06-20 17:27:39 | INFO  | main.py        :lifespan             | FastAPI应用启动完成
2025-06-20 17:31:41 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-20 17:31:41 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-20 17:31:41 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-20 17:31:41 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-20 17:31:41 | INFO  | main.py        :lifespan             | FastAPI应用启动中...
2025-06-20 17:31:41 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-20 17:31:41 | INFO  | base.py        :start                | Scheduler started
2025-06-20 17:31:41 | INFO  | main.py        :lifespan             | 定时任务已启动
2025-06-20 17:31:41 | INFO  | main.py        :lifespan             | FastAPI应用启动完成
