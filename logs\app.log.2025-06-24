2025-06-24 14:25:46 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-24 14:25:46 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-24 14:25:46 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-24 14:25:46 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-24 14:25:46 | INFO  | main.py        :lifespan             | FastAPI应用启动中...
2025-06-24 14:25:46 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-24 14:25:46 | INFO  | base.py        :start                | Scheduler started
2025-06-24 14:25:46 | INFO  | main.py        :lifespan             | 定时任务已启动
2025-06-24 14:25:46 | INFO  | main.py        :lifespan             | FastAPI应用启动完成
2025-06-24 14:26:30 | INFO  | main.py        :log_requests         | [POST]/openapi/do -- (from=192.168.5.235)
2025-06-24 14:28:18 | INFO  | main.py        :log_requests         | [POST]/openapi/do? -- 200 (from=192.168.5.235,2025-06-24 14:26:30) - 108.517s
2025-06-24 14:28:46 | INFO  | main.py        :log_requests         | [POST]/openapi/do -- (from=192.168.5.235)
2025-06-24 14:28:56 | INFO  | main.py        :log_requests         | [POST]/openapi/do? -- 200 (from=192.168.5.235,2025-06-24 14:28:46) - 10.190s
2025-06-24 14:36:04 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-24 14:36:05 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-24 14:36:05 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-24 14:36:05 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-24 14:36:05 | INFO  | main.py        :lifespan             | FastAPI应用启动中...
2025-06-24 14:36:05 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-24 14:36:05 | INFO  | base.py        :start                | Scheduler started
2025-06-24 14:36:05 | INFO  | main.py        :lifespan             | 定时任务已启动
2025-06-24 14:36:05 | INFO  | main.py        :lifespan             | FastAPI应用启动完成
2025-06-24 14:36:15 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-24 14:36:15 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-24 14:36:15 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-24 14:36:15 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-24 14:36:15 | INFO  | main.py        :lifespan             | FastAPI应用启动中...
2025-06-24 14:36:15 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-24 14:36:15 | INFO  | base.py        :start                | Scheduler started
2025-06-24 14:36:15 | INFO  | main.py        :lifespan             | 定时任务已启动
2025-06-24 14:36:15 | INFO  | main.py        :lifespan             | FastAPI应用启动完成
2025-06-24 14:36:45 | INFO  | main.py        :log_requests         | [POST]/openapi/do -- (from=192.168.5.235)
2025-06-24 14:36:51 | INFO  | main.py        :log_requests         | [POST]/openapi/do? -- 200 (from=192.168.5.235,2025-06-24 14:36:45) - 5.671s
2025-06-24 14:39:27 | INFO  | main.py        :log_requests         | [POST]/openapi/do -- (from=192.168.5.235)
2025-06-24 14:39:31 | INFO  | main.py        :log_requests         | [POST]/openapi/do? -- 200 (from=192.168.5.235,2025-06-24 14:39:27) - 3.965s
