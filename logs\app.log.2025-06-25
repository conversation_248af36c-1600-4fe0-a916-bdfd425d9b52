2025-06-25 17:59:44 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-25 17:59:45 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-25 17:59:45 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-25 17:59:45 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-25 17:59:45 | INFO  | main.py        :lifespan             | FastAPI应用启动中...
2025-06-25 17:59:45 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-25 17:59:45 | INFO  | base.py        :start                | Scheduler started
2025-06-25 17:59:45 | INFO  | main.py        :lifespan             | 定时任务已启动
2025-06-25 17:59:45 | INFO  | main.py        :lifespan             | FastAPI应用启动完成
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/ -- (from=192.168.5.235)
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/? -- 200 (from=192.168.5.235,2025-06-25 17:59:46) - 0.058s
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css -- (from=192.168.5.235)
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css -- (from=192.168.5.235)
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js -- (from=192.168.5.235)
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js -- (from=192.168.5.235)
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js -- (from=192.168.5.235)
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js -- (from=192.168.5.235)
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css? -- 304 (from=192.168.5.235,2025-06-25 17:59:46) - 0.017s
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js? -- 304 (from=192.168.5.235,2025-06-25 17:59:46) - 0.013s
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js? -- 304 (from=192.168.5.235,2025-06-25 17:59:46) - 0.014s
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js? -- 304 (from=192.168.5.235,2025-06-25 17:59:46) - 0.013s
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css? -- 304 (from=192.168.5.235,2025-06-25 17:59:46) - 0.020s
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js? -- 304 (from=192.168.5.235,2025-06-25 17:59:46) - 0.019s
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js -- (from=192.168.5.235)
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js -- (from=192.168.5.235)
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js -- (from=192.168.5.235)
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js -- (from=192.168.5.235)
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js -- (from=192.168.5.235)
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js -- (from=192.168.5.235)
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js? -- 304 (from=192.168.5.235,2025-06-25 17:59:46) - 0.009s
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js? -- 304 (from=192.168.5.235,2025-06-25 17:59:46) - 0.012s
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js? -- 304 (from=192.168.5.235,2025-06-25 17:59:46) - 0.012s
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js? -- 304 (from=192.168.5.235,2025-06-25 17:59:46) - 0.012s
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js? -- 304 (from=192.168.5.235,2025-06-25 17:59:46) - 0.020s
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js? -- 304 (from=192.168.5.235,2025-06-25 17:59:46) - 0.018s
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js -- (from=192.168.5.235)
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js -- (from=192.168.5.235)
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js? -- 304 (from=192.168.5.235,2025-06-25 17:59:46) - 0.008s
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js? -- 200 (from=192.168.5.235,2025-06-25 17:59:46) - 0.010s
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/plat/getall -- (from=192.168.5.235)
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=192.168.5.235,2025-06-25 17:59:46) - 0.009s
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/common/getapi -- (from=192.168.5.235)
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=192.168.5.235,2025-06-25 17:59:46) - 0.022s
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [POST]/order/get -- (from=192.168.5.235)
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=192.168.5.235,2025-06-25 17:59:46) - 0.009s
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [POST]/order/getById -- (from=192.168.5.235)
2025-06-25 17:59:46 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=192.168.5.235,2025-06-25 17:59:46) - 0.002s
2025-06-25 17:59:48 | INFO  | main.py        :log_requests         | [POST]/goods/get -- (from=192.168.5.235)
2025-06-25 17:59:48 | INFO  | main.py        :log_requests         | [POST]/goods/get? -- 200 (from=192.168.5.235,2025-06-25 17:59:48) - 0.004s
2025-06-25 17:59:48 | INFO  | main.py        :log_requests         | [POST]/goods/getById -- (from=192.168.5.235)
2025-06-25 17:59:48 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- 200 (from=192.168.5.235,2025-06-25 17:59:48) - 0.003s
2025-06-25 17:59:49 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-25 17:59:49 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-25 17:59:49) - 0.006s
2025-06-25 17:59:49 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-25 17:59:49 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-25 17:59:49) - 0.002s
2025-06-25 17:59:50 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-25 17:59:50 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-25 17:59:50) - 0.003s
2025-06-25 18:00:28 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map -- (from=192.168.5.235)
2025-06-25 18:00:28 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map? -- 404 (from=192.168.5.235,2025-06-25 18:00:28) - 0.001s
2025-06-25 18:02:54 | INFO  | main.py        :log_requests         | [GET]/ -- (from=192.168.5.235)
2025-06-25 18:02:54 | INFO  | main.py        :log_requests         | [GET]/? -- 200 (from=192.168.5.235,2025-06-25 18:02:54) - 0.002s
2025-06-25 18:02:54 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js -- (from=192.168.5.235)
2025-06-25 18:02:55 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js? -- 304 (from=192.168.5.235,2025-06-25 18:02:54) - 0.003s
2025-06-25 18:02:55 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map -- (from=192.168.5.235)
2025-06-25 18:02:55 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map? -- 404 (from=192.168.5.235,2025-06-25 18:02:55) - 0.001s
2025-06-25 18:02:55 | INFO  | main.py        :log_requests         | [GET]/plat/getall -- (from=192.168.5.235)
2025-06-25 18:02:55 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=192.168.5.235,2025-06-25 18:02:55) - 0.008s
2025-06-25 18:02:55 | INFO  | main.py        :log_requests         | [GET]/common/getapi -- (from=192.168.5.235)
2025-06-25 18:02:55 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=192.168.5.235,2025-06-25 18:02:55) - 0.017s
2025-06-25 18:02:55 | INFO  | main.py        :log_requests         | [GET]/static/favicon.ico -- (from=192.168.5.235)
2025-06-25 18:02:55 | INFO  | main.py        :log_requests         | [GET]/static/favicon.ico? -- 200 (from=192.168.5.235,2025-06-25 18:02:55) - 0.002s
2025-06-25 18:02:55 | INFO  | main.py        :log_requests         | [POST]/order/get -- (from=192.168.5.235)
2025-06-25 18:02:55 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=192.168.5.235,2025-06-25 18:02:55) - 0.009s
2025-06-25 18:02:55 | INFO  | main.py        :log_requests         | [POST]/order/getById -- (from=192.168.5.235)
2025-06-25 18:02:55 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=192.168.5.235,2025-06-25 18:02:55) - 0.003s
2025-06-25 18:02:56 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-25 18:02:56 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-25 18:02:56) - 0.006s
2025-06-25 18:02:56 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-25 18:02:56 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-25 18:02:56) - 0.002s
2025-06-25 18:02:58 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-25 18:02:58 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-25 18:02:58) - 0.004s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/ -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.001s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.014s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.014s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.014s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.014s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.014s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.015s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.012s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.009s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.015s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.010s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.014s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.009s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.008s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.009s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.009s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.011s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.009s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.009s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.007s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.007s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.012s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map? -- 404 (from=192.168.5.235,2025-06-25 18:03:12) - 0.002s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/plat/getall -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.010s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/common/getapi -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.008s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.001s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/favicon.ico -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [GET]/static/favicon.ico? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.002s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [POST]/order/get -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.015s
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [POST]/order/getById -- (from=192.168.5.235)
2025-06-25 18:03:12 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=192.168.5.235,2025-06-25 18:03:12) - 0.003s
2025-06-25 18:03:13 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-25 18:03:13 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-25 18:03:13) - 0.005s
2025-06-25 18:03:13 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-25 18:03:13 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-25 18:03:13) - 0.002s
2025-06-25 18:03:15 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-25 18:03:15 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-25 18:03:15) - 0.004s
