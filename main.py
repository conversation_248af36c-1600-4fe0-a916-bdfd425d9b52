import os
import time
import logging
from typing import Dict, Any
from contextlib import asynccontextmanager

from fastapi import FastAPI, Request
from fastapi.datastructures import FormData
from fastapi.responses import FileResponse, JSONResponse, Response
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware

# 初始化日志
from log import Logger
logger_instance = Logger()
logger_instance.init_fastapi_app()
logging.info("日志系统初始化完成")

# 定时任务相关
scheduler_enabled = True
try:
    from core.taskScheduler import scheduler
    logging.info("定时任务模块加载成功")
except Exception as e:
    logging.warning(f"定时任务模块加载失败: {str(e)}")
    scheduler_enabled = False

# 简单直接的服务初始化
from core.service import (
    polyService, shopService, orderService,
    goodsService, recordService, platService,
    commonExampleService
)

# 全局服务实例
polySrv = polyService()
shopSrv = shopService()
orderSrv = orderService()
goodsSrv = goodsService()
recordSrv = recordService()
platSrv = platService()
commonSrv = commonExampleService()

logging.info("所有服务初始化完成")

def json_response(content):
    """
    返回JSON响应，避免双重编码
    用于处理服务方法已经返回JSON字符串的情况
    """
    return Response(content=content, media_type="application/json")

# 根目录
root = os.path.dirname(os.path.abspath(__file__))

# 定义 lifespan 事件处理器
@asynccontextmanager
async def lifespan(fastapi_app: FastAPI):  # fastapi_app 参数仅用于类型提示
    # 启动时执行
    try:
        logging.info("FastAPI应用启动中...")

        # 启动定时任务
        if scheduler_enabled:
            try:
                scheduler.start()
                logging.info("定时任务已启动")
            except Exception as e:
                logging.error(f"定时任务启动失败: {str(e)}")

        logging.info("FastAPI应用启动完成")

    except Exception as e:
        logging.error(f"应用启动时发生错误: {str(e)}")

    # yield 控制权给应用
    yield

    # 关闭时执行
    try:
        if scheduler_enabled:
            scheduler.shutdown()
            logging.info("定时任务已关闭")
    except Exception as e:
        logging.error(f"应用关闭时发生错误: {str(e)}")

# 创建FastAPI应用
app = FastAPI(
    title="模拟网关后台",
    description="Flask到FastAPI迁移版本",
    version="2.0.0",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(start_time))
    
    # 构建完整的请求路径（包含查询参数）
    full_path = request.url.path
    if request.url.query:
        full_path += "?" + request.url.query
    
    # 记录请求开始
    logging.info(f'[{request.method}]{full_path} -- (from={request.client.host})')
    
    try:
        # 处理请求参数（异步）
        request.state.postvars = await get_request_vars(request)
        request.state.postvars['remote_addr'] = request.client.host
        request.state.postvars['_timestamp'] = timestamp
    except Exception as e:
        logging.error(f"Request processing error: {str(e)}")
        request.state.postvars = {
            'remote_addr': request.client.host,
            '_timestamp': timestamp
        }
    
    # 继续处理请求
    response = await call_next(request)
    
    # 记录请求结束
    process_time = time.time() - start_time
    logging.info(f'[{request.method}]{request.url.path}?{request.url.query} -- {response.status_code} (from={request.client.host},{timestamp}) - {process_time:.3f}s')
    
    return response

# 静态文件服务
app.mount("/static", StaticFiles(directory="static"), name="static")

# 旧的 on_event 装饰器已被 lifespan 事件处理器替代

# 请求参数整合函数
async def get_request_vars(request: Request) -> Dict[str, Any]:
    """整合请求参数（查询参数 + 表单数据）并转换为小写键名的字典"""

    postvars = {}
    
    # 处理查询参数（GET参数）
    if request.query_params:
        postvars.update(request.query_params.multi_items())
    
    # 处理表单数据（POST参数）
    content_type = request.headers.get("content-type", "").lower()    
    if "application/x-www-form-urlencoded" in content_type or "multipart/form-data" in content_type:
        try:
            # 使用正确的异步方式读取表单数据
            form_data: FormData = await request.form()
            if form_data:
                postvars.update(form_data.multi_items())
        except Exception as e:
            logging.error(f"Form data parsing error: {str(e)}")
    
    # 转换键名为小写并处理值
    result = {}
    for key, value in postvars.items():
        # 处理多值字段（取第一个值）
        if isinstance(value, list) and len(value) > 0:
            result[key.lower()] = value[0]
        else:
            result[key.lower()] = value
    
    return result

# 路由定义
@app.get("/")
async def index():
    return FileResponse(os.path.join(root, "static/index.html"))

@app.get("/log")
async def log_page():
    return FileResponse(os.path.join(root, "static/log.html"))

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "ok",
        "message": "FastAPI应用运行正常",
        "framework": "FastAPI",
        "version": "2.0.0",
        "scheduler_enabled": scheduler_enabled
    }

@app.get("/test")
async def test():
    return {"message": "测试接口正常", "framework": "FastAPI", "version": "2.0.0"}

@app.get("/common/getCurrentIP")
async def get_current_ip(request: Request):
    """获取当前用户IP地址"""
    client_ip = request.state.postvars.get('remote_addr', '127.0.0.1')
    return Response(content=client_ip, media_type="text/plain")

# 业务API路由
@app.api_route("/common/getapi", methods=["GET", "POST"], tags=["聚合网关"])
async def get_api():
    try:
        result = polySrv.getApiAll()
        return json_response(result)
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/polymsg/getApiByType", methods=["GET", "POST"], tags=["消息推送"])
async def get_api_by_type(request: Request):
    try:
        result = polySrv.getApiByType(request.state.postvars)
        return json_response(result)
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/polymsg/getById", methods=["GET", "POST"], tags=["消息推送"])
async def get_api_detail(request: Request):
    try:
        result = polySrv.getApiDetail(request.state.postvars)
        return json_response(result)
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/openapi/do", methods=["GET", "POST"], tags=["聚合网关"])
async def open_api(request: Request):
    try:
        result = polySrv.doBusiness(request.state.postvars)
        return json_response(result)
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

# 记录相关路由
@app.api_route("/record/get", methods=["GET", "POST"], tags=["日志"])
async def get_record(request: Request):
    try:
        result = recordSrv.getRecords(request.state.postvars)
        return json_response(result)
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/record/detail", methods=["GET", "POST"], tags=["日志"])
async def get_record_detail(request: Request):
    try:
        result = recordSrv.getRecordDetail(request.state.postvars)
        return json_response(result) 
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

# 订单相关路由
@app.api_route("/order/get", methods=["GET", "POST"], tags=["订单"])
async def get_order(request: Request):
    try:
        result = orderSrv.getExample(request.state.postvars)
        return json_response(result)
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/order/add", methods=["GET", "POST"], tags=["订单"])
async def add_order(request: Request):
    try:
        result = orderSrv.addExample(request.state.postvars)
        return json_response(result) 
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/order/update", methods=["GET", "POST"], tags=["订单"])
async def update_order(request: Request):
    try:
        result = orderSrv.updateExample(request.state.postvars)
        return json_response(result) 
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/order/del", methods=["GET", "POST"], tags=["订单"])
async def delete_order(request: Request):
    try:
        result = orderSrv.delExample(request.state.postvars)
        return json_response(result) 
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/order/getById", methods=["GET", "POST"], tags=["订单"])
async def get_order_by_id(request: Request):
    try:
        result = orderSrv.getById(request.state.postvars)
        return json_response(result) 
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/order/push", methods=["GET", "POST"], tags=["订单"])
async def push_order(request: Request):
    try:
        result = orderSrv.pushByExample(request.state.postvars)
        return json_response(result) 
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/order/pushMany", methods=["GET", "POST"], tags=["订单"])
async def push_many_order(request: Request):
    try:
        result = orderSrv.pushByExampleMany(request.state.postvars)
        return json_response(result) 
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/order/pushNotice", methods=["GET", "POST"], tags=["订单"])
async def push_notice(request: Request):
    try:
        result = orderSrv.pushNotice(request.state.postvars)
        return json_response(result) 
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/order/make", methods=["GET", "POST"], tags=["订单"])
async def make_order(request: Request):
    try:
        result = orderSrv.makeOrder(request.state.postvars)
        return json_response(result) 
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/order/openapipush", methods=["GET", "POST"], tags=["订单"])
async def openapi_push(request: Request):
    try:
        result = orderSrv.pushByExample(request.state.postvars)
        return json_response(result) 
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

# 店铺相关路由
@app.api_route("/shop/getShopByPlat", methods=["GET", "POST"], tags=["店铺"])
async def shop(request: Request):
    try:
        result = shopSrv.getShopList(request.state.postvars)
        return json_response(result) 
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

# 平台相关路由
@app.api_route("/plat/getall", methods=["GET", "POST"], tags=["平台"])
async def get_plats():
    try:
        result = platSrv.getPlatList()
        return json_response(result)
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/plat/add", methods=["GET", "POST"], tags=["平台"])
async def add_plat(request: Request):
    try:
        result = platSrv.addPlat(request.state.postvars)
        return json_response(result) 
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

# 商品相关路由
@app.api_route("/goods/get", methods=["GET", "POST"], tags=["商品"])
async def get_goods(request: Request):
    try:
        result = goodsSrv.getGoodsBrief(request.state.postvars)
        return json_response(result) 
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/goods/getById", methods=["GET", "POST"], tags=["商品"])
async def get_goods_by_id(request: Request):
    try:
        result = goodsSrv.getById(request.state.postvars)
        return json_response(result) 
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/goods/add", methods=["GET", "POST"], tags=["商品"])
async def add_goods(request: Request):
    try:
        result = goodsSrv.addGoods(request.state.postvars)
        return json_response(result) 
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/goods/update", methods=["GET", "POST"], tags=["商品"])
async def update_goods(request: Request):
    try:
        result = goodsSrv.updateGoods(request.state.postvars)
        return json_response(result) 
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/goods/del", methods=["GET", "POST"], tags=["商品"])
async def del_goods(request: Request):
    try:
        result = goodsSrv.delGoods(request.state.postvars)
        return json_response(result) 
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

# 通用样例相关路由
@app.api_route("/common/get", methods=["GET", "POST"], tags=["通用样例"])
async def get_common(request: Request):
    try:
        result = commonSrv.getExample(request.state.postvars)
        return json_response(result) 
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/common/getById", methods=["GET", "POST"], tags=["通用样例"])
async def get_common_by_id(request: Request):
    try:
        result = commonSrv.getById(request.state.postvars)
        return json_response(result) 
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/common/add", methods=["GET", "POST"], tags=["通用样例"])
async def add_common(request: Request):
    try:
        result = commonSrv.addExample(request.state.postvars)
        return json_response(result) 
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/common/update", methods=["GET", "POST"], tags=["通用样例"])
async def update_common(request: Request):
    try:
        result = commonSrv.updateExample(request.state.postvars)
        return json_response(result) 
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/common/del", methods=["GET", "POST"], tags=["通用样例"])
async def del_common(request: Request):
    try:
        result = commonSrv.delExample(request.state.postvars)
        return json_response(result)
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/common/getFieldMappingBreif", methods=["GET", "POST"], tags=["通用样例"])
async def getFieldMappingBreif(request: Request):
    try:
        result = commonSrv.getFieldMappingBreif(request.state.postvars)
        return result
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)
    
@app.api_route("/common/getFieldMapping", methods=["GET", "POST"], tags=["通用样例"])
async def get_field_mapping(request: Request):
    try:
        result = commonSrv.getFieldMapping(request.state.postvars)
        return json_response(result)
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/common/updateFieldMapping", methods=["GET", "POST"], tags=["通用样例"])
async def update_field_mapping(request: Request):
    try:
        result = commonSrv.updateFieldMapping(request.state.postvars)
        return json_response(result)
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/common/addFieldMapping", methods=["GET", "POST"], tags=["通用样例"])
async def add_field_mapping(request: Request):
    try:
        result = commonSrv.addFieldMapping(request.state.postvars)
        return json_response(result)
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/common/delFieldMapping", methods=["GET", "POST"], tags=["通用样例"])
async def del_field_mapping(request: Request):
    try:
        result = commonSrv.delFieldMapping(request.state.postvars)
        return json_response(result)
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)
    
if __name__ == "__main__":
    import uvicorn
    print("启动FastAPI应用...")
    uvicorn.run(app, host="0.0.0.0", port=9083, log_level="info")
