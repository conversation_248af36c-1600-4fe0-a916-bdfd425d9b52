# 部署说明



#### 依赖安装

```bash
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```



本项目包含两种框架启动方式



## FastApi

### 1. 文件
- `main.py` - fastapi启动入口
- `gunicorn.conf.py` - Gunicorn配置文件
- `start.sh` - 启动脚本
- `stop.sh` - 停止脚本

### 2. 启动应用

#### 开发环境
```bash
# 直接运行
python main.py

# 或使用uvicorn
uvicorn main:app --host 0.0.0.0 --port 9083 --reload
```

#### 生产环境
```bash
# 使用启动脚本
chmod +x start.sh
./start.sh

# 或直接使用gunicorn
gunicorn main:app -c gunicorn.conf.py
```

### 3. 停止应用
```bash
# 使用停止脚本
chmod +x stop.sh
./stop.sh

# 或手动停止
pkill -f "gunicorn main:app"
```

### 4. 日志文件
- 应用日志: `./logs/app.log`
- Gunicorn访问日志: `./logs/gunicorn_access.log`
- Gunicorn错误日志: `./logs/gunicorn_error.log`
- 启动日志: `./logs/startup.log`



---



## Flask

### 1. 文件

- `server.py` - flask启动入口
- `uwsgi.ini` - 保留原uWSGI配置

### 2. 启动应用

#### 开发环境

```bash
# 直接运行
python server.py
```

#### 生产环境

```bash
# 使用启动脚本
chmod +x flask_start.sh
./flask_start.sh

# 或直接使用uwsgi
nohup uwsgi uwsgi.ini >/dev/null 2>&1 &

# centos7 部署流程参考
https://blog.csdn.net/u010067848/article/details/84826340
```



### 3. 停止应用

```bash
# 使用停止脚本
chmod +x flask_stop.sh
./flask_stop.sh

# 或手动停止
killall -9 uwsgi
或 pkill -f "uwsgi uwsgi.ini"
```



### 4. 日志文件

- 应用日志: `./logs/app.log`

  



