#!/usr/bin/env python3
"""
快速启动脚本
自动检测环境并选择合适的启动方式
"""

import os
import sys
import platform

def main():
    """主函数"""
    print("=" * 60)
    print("Flask到FastAPI迁移项目 - 快速启动")
    print("=" * 60)
    
    # 检测操作系统
    system = platform.system()
    print(f"操作系统: {system}")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 检查依赖
    try:
        import fastapi
        import uvicorn
        print(f"FastAPI版本: {fastapi.__version__}")
        print(f"Uvicorn版本: {uvicorn.__version__}")
    except ImportError as e:
        print(f"❌ 依赖检查失败: {e}")
        print("请先安装依赖: pip install -r requirements.txt")
        sys.exit(1)
    
    print("=" * 60)
    
    # 选择启动方式
    if system == "Windows":
        print("🪟 检测到Windows系统，使用Uvicorn启动")
        print("启动命令: python start_windows.py")
        print("=" * 60)
        
        # 导入并启动
        try:
            from start_windows import main as start_main
            start_main()
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            sys.exit(1)
    
    else:
        print("🐧 检测到Linux/Unix系统")
        
        # 检查是否有gunicorn
        try:
            import gunicorn
            print(f"Gunicorn版本: {gunicorn.__version__}")
            print("推荐使用Gunicorn启动: gunicorn main:app -c gunicorn.conf.py")
        except ImportError:
            print("⚠️  未安装Gunicorn，将使用Uvicorn启动")
        
        print("启动命令: python main.py")
        print("=" * 60)
        
        # 直接启动
        try:
            import uvicorn
            uvicorn.run("main:app", host="0.0.0.0", port=9083, log_level="info")
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            sys.exit(1)

if __name__ == "__main__":
    main()
