import os
import time

from flask import Flask, request, send_from_directory

from core.service import *
from core.taskScheduler import scheduler

# 全局变量

app = Flask(__name__)
# 初始化logger
logger.init_app(app)
# 根目录
root = os.path.dirname(os.path.abspath(__file__))

# 定时任务
scheduler.start()

# 服务初始化
polySrv = polyService()
shopSrv = shopService()
orderSrv = orderService()
goodsSrv = goodsService()
recordSrv = recordService()
platSrv = platService()
commonSrv = commonExampleService()

@app.before_request
def pre_process():
    app.logger.info('[{}]{} -- (from={})'.format(request.method, request.full_path, request.remote_addr))
    # 请求参数预处理
    request.postvars = getRequestVars()
    request.postvars['remote_addr'] = request.remote_addr
    request.postvars['_timestamp'] = time.strftime(
        "%Y-%m-%d %H:%I:%S", time.localtime(time.time()))
    return None


@app.after_request
def after_request(response):
    app.logger.info('[{}]{} -- {} (from={},{})'.format(request.method, request.full_path, response.status, request.remote_addr, request.postvars['_timestamp']))
    return response


@app.route('/')
def index():
    return send_from_directory(root, "static/index.html")

@app.route('/log')
def log():
    return send_from_directory(root, "static/log.html")

@app.route('/static/<path:path>')
def send_static(path):
    return send_from_directory('static', path)

@app.route('/common/getapi', methods=['GET', 'POST'])
def getApi():
    return polySrv.getApiAll()

@app.route('/polymsg/getApiByType', methods=['GET', 'POST'])
def getApiByType():
    return polySrv.getApiByType(request.postvars)

@app.route('/polymsg/getById', methods=['GET', 'POST'])
def getApiDetail():
    return polySrv.getApiDetail(request.postvars)

@app.route('/openapi/do', methods=['GET', 'POST'])
def openApi():
    return polySrv.doBusiness(request.postvars)


@app.route('/record/get', methods=['GET', 'POST'])
def getrecord():
    return recordSrv.getRecords(request.postvars)

@app.route('/record/detail', methods=['GET', 'POST'])
def getRecordDetail():
    return recordSrv.getRecordDetail(request.postvars)


@app.route('/order/get', methods=['GET', 'POST'])
def getorder():
    return orderSrv.getExample(request.postvars)

@app.route('/order/add', methods=['GET', 'POST'])
def addorder():
    return orderSrv.addExample(request.postvars)

@app.route('/order/update', methods=['GET', 'POST'])
def updateorder():
    return orderSrv.updateExample(request.postvars)

@app.route('/order/del', methods=['GET', 'POST'])
def deleteorder():
    return orderSrv.delExample(request.postvars)

@app.route('/order/getById', methods=['GET', 'POST'])
def getOrderById():
    return orderSrv.getById(request.postvars)

@app.route('/order/push', methods=['GET', 'POST'])
def pushorder():
    return orderSrv.pushByExample(request.postvars)

@app.route('/order/pushMany', methods=['GET', 'POST'])
def pushmanyorder():
    return orderSrv.pushByExampleMany(request.postvars)

@app.route('/order/pushNotice', methods=['GET', 'POST'])
def pushNotice():
    return orderSrv.pushNotice(request.postvars)

@app.route('/order/make', methods=['GET', 'POST'])
def makeorder():
    return orderSrv.makeOrder(request.postvars)

@app.route('/order/openapipush', methods=['GET', 'POST'])
def openapipush():
    return orderSrv.pushByExample(request.postvars)

@app.route('/shop/getShopByPlat', methods=['GET', 'POST'])
def shop():
    return shopSrv.getShopList(request.postvars)

@app.route('/plat/getall', methods=['GET', 'POST'])
def getPlats():
    return platSrv.getPlatList()

@app.route('/plat/add', methods=['GET', 'POST'])
def addPlat():
    return platSrv.addPlat(request.postvars)


@app.route('/goods/get', methods=['GET', 'POST'])
def getgoods():
    return goodsSrv.getGoodsBrief(request.postvars)

@app.route('/goods/getById', methods=['GET', 'POST'])
def getGoodsById():
    return goodsSrv.getById(request.postvars)

@app.route('/goods/add', methods=['GET', 'POST'])
def addgoods():
    return goodsSrv.addGoods(request.postvars)

@app.route('/goods/update', methods=['GET', 'POST'])
def updategoods():
    return goodsSrv.updateGoods(request.postvars)

@app.route('/goods/del', methods=['GET', 'POST'])
def delgoods():
    return goodsSrv.delGoods(request.postvars)


@app.route('/common/get', methods=['GET', 'POST'])
def getCommon():
    return commonSrv.getExample(request.postvars)

@app.route('/common/getById', methods=['GET', 'POST'])
def getCommonById():
    return commonSrv.getById(request.postvars)

@app.route('/common/getFieldMappingBreif', methods=['GET', 'POST'])
def getFieldMappingBreif():
    return commonSrv.getFieldMappingBreif(request.postvars)

@app.route('/common/add', methods=['GET', 'POST'])
def addCommon():
    return commonSrv.addExample(request.postvars)

@app.route('/common/update', methods=['GET', 'POST'])
def updateCommon():
    return commonSrv.updateExample(request.postvars)

@app.route('/common/del', methods=['GET', 'POST'])
def delCommon():
    return commonSrv.delExample(request.postvars)


# 请求参数整合
def getRequestVars():
    postvars = {}
    if len(request.args) > 0:
        postvars.update(request.args)
    if len(request.form) > 0:
        postvars.update(request.form)
    # key转小写
    newDic = {}
    for key, value in postvars.items():
        if isinstance(value, list) and len(value) > 0:
            newDic[key.lower()] = value[0]
        else:
            newDic[key.lower()] = value
    return newDic


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=9083, threaded=True)
