#!/usr/bin/env python3
"""
Windows兼容的启动脚本
在Windows环境下，Gunicorn不可用，使用uvicorn替代
"""

import os
import sys
import uvicorn
import logging

def main():
    """启动FastAPI应用"""
    
    # 设置工作目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    print("=" * 50)
    print("启动FastAPI应用 (Windows版本)")
    print("=" * 50)
    print(f"工作目录: {os.getcwd()}")
    print(f"Python版本: {sys.version}")
    print("=" * 50)
    
    # 配置参数 (对应gunicorn.conf.py的配置)
    config = {
        "app": "main:app",
        "host": "0.0.0.0",
        "port": 9083,
        "workers": 1,  # Windows下uvicorn不支持多worker
        "log_level": "info",
        "access_log": True,
        "reload": False,  # 生产环境不使用reload
    }
    
    print(f"启动配置:")
    print(f"  - 地址: {config['host']}:{config['port']}")
    print(f"  - Workers: {config['workers']}")
    print(f"  - 日志级别: {config['log_level']}")
    print("=" * 50)
    
    try:
        # 启动应用
        uvicorn.run(**config)
    except KeyboardInterrupt:
        print("\n应用已停止")
    except Exception as e:
        print(f"启动失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
