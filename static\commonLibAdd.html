<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="Access-Control-Allow-Origin" content="*" />
  <title>通用样例库-新增</title>

  <script src="./js/jquery.min.js"></script>
  <script src="./js/layui/layui.js"></script>
  
  <link rel="stylesheet" href="./js/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="./js/layui/css/layui-theme-dark.css" id="layui_theme_css" >

  <style>
    .layui-textarea {
      height: 450px;
    }
    .form-submit {
      float: right;
      margin-right: 65px;
    }
    .md-select {
      width: 300px !important;
    }
  </style>

</head>

<body>
  <div>
    <form class="layui-form" action="" style="margin-right: 40px; margin-top: 20px;">
      <div class="layui-form-item">
        <label class="layui-form-label">平台</label>
        <div class="layui-input-inline md-select">
          <select id="plat" name="plat" lay-verify="required" lay-search="">
          </select>
        </div>
      </div>
      <div class="layui-form-item">
        <label class="layui-form-label">接口</label>
        <div class="layui-input-inline md-select">
          <select class="method_choose" lay-verify="required" id="method_choose" name="method" lay-filter="method_change" lay-search=""></select>
        </div>
        <label style="color: red;"><strong>抓单、下载商品 接口会根据样例库返回, 不可配置; <br>发货 接口根据请求内容返回, 无需配置</strong></label>
      </div>      
      <div class="layui-form-item" id="biz-feild-box">
        <label class="layui-form-label">字段</label>
        <div class="layui-input-inline md-select">
          <input type="text" name="biz_feild" autocomplete="off" class="layui-input">
        </div>
        <label style="display: block; padding: 9px 15px;color: red;"><strong>不支持简单类型字段配置(如string、int、bool);</strong></label>
      </div>
      <div class="layui-form-item">
        <label class="layui-form-label" id="content-box-title">字段内容</label>
        <div class="layui-input-block">
          <textarea name="content" lay-verify="required" placeholder="请输入内容" class="layui-textarea"></textarea>
        </div>
      </div>
      <div class="layui-form-item">
        <label class="layui-form-label">标签</label>
        <div class="layui-input-block">
          <input type="text" name="tag" autocomplete="off" class="layui-input">
        </div>
      </div>
      <div class="layui-form-item">
        <div class="layui-input-block form-submit">
          <button type="submit" class="layui-btn" lay-submit="" lay-filter="post">立即提交</button>
          <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </div>
      </div>
    </form>

  </div>

  <div style="min-height: 100%; margin-bottom: -100px;">
    <table id="demo" lay-filter="test" style="width: 100%;"></table>
  </div>

<script>

    $(function () {
      // 加载平台列表    
      $.ajax({
        url: "/plat/getall",
        type: 'GET',
        dataType: 'json',
        async: false,
        success: function (data) {
          // 平台列表
          var platAll = [{ "PlatValue": "", "Name": "" }, { "PlatValue": 0, "Name": "--通用--" }];
          platAll = platAll.concat(data);
          // 填充下拉列表
          $.each(platAll, function (i, item) {
            var optText = '[' + item.PlatValue + ']' + item.Name;
            if (optText == '[]') {
              optText = ''
            }
            var options = "<option value='" + item.PlatValue + "'>" + optText + "</option>";
            $("#plat").append(options);
          });
          // 重新渲染表单
          layui.form.render();
        },
        error: function (ex) {
          console.log(ex)
        }
      });

      // 加载接口列表    
      $.ajax({
        url: "/common/getapi",
        type: 'GET',
        dataType: 'json',
        async: false,
        success: function (data) {
          var apiAll = [{ "apitype": "", "name": "" }];
          var apiBusiness = data.find(a => a.type == "Business");
          if (apiBusiness) {
            apiAll = apiAll.concat(apiBusiness.apitypes);
          }
          var apiCommom = data.find(a => a.type == "Commom");
          if (apiCommom) {
            apiAll = apiAll.concat(apiCommom.apitypes);
          }
          if (apiAll.length > 0) {
            $.each(apiAll, function (i, item) {
              var options = "<option value='" + item.apitype + "'>" + item.name + "</option>";
              $(".method_choose").append(options);
            });
            // 重新渲染表单
            layui.form.render();
          }
        },
        error: function (ex) {
          layer.open({
            type: 1,
            area: ['512px', '384px'],
            title: '加载聚合接口列表出错',
            closeBtn: 0,
            shadeClose: true,
            content: ex.responseText
          });
        }
      });
    })

    layui.config({
      version: '1626897823561' //为了更新 js 缓存，可忽略
    });

    //加载模块  
    layui.use(function () { //亦可加载特定模块：layui.use(['layer', 'laydate', function(){
      //得到各种内置组件
      var layer = layui.layer //弹层
        , element = layui.element //元素操作
        , dropdown = layui.dropdown //下拉菜单
        , form = layui.form

      // 监听提交
      form.on('submit(post)', function (data) {
        if (data.field.content == '') {
          layer.alert('请不要录入空数据', { icon: 2, title: '错误' })
          return false;
        }

        if (data.field.biz_feild == '' && data.field.method.toLowerCase().startsWith('differ.jh.business.')) {
          layer.alert('电商业务接口，请明确具体字段', { icon: 2, title: '错误' })
          return false;
        }

        var obj = null;
        // 订单数据格式校验
        try {
          obj = window.parent.jsonlint.parse(data.field.content.toLowerCase());
          if (!obj || typeof obj != 'object') {
            layer.alert('数据格式非object, 请检查后重新录入', { icon: 2, title: '错误' })
            return false;
          }
        } catch (e) {
          layer.alert('数据格式不正确，请检查后重新录入', { icon: 2, title: '错误' })
          return false;
        }
        // 压缩
        var t = data.field.content.replace(/[\r\n]/g, "")
        for (var n = [], o = !1, i = 0, r = t.length; r > i; i++) {
          var a = t.charAt(i);
          o && a === o ? "\\" !== t.charAt(i - 1) && (o = !1) : o || '"' !== a && "'" !== a ? o || " " !== a && "	" !== a || (a = "") : o = a, n.push(a)
        }
        dataContent = n.join("");

        var post_data = {
           plat: data.field.plat ?? 0, 
           tag: data.field.tag, 
           biz_feild: data.field.biz_feild, 
           method: data.field.method,
           content: dataContent
        };
        // 提交   
        $.ajax({
          url: "/common/add",
          type: 'POST',
          async: false,
          data: post_data,
          dataType: "text",
          success: function (data) {
            if (data == 'success') {
              var mylay = parent.layer.getFrameIndex(window.name);
              parent.layer.msg('已提交', { icon: 1 });
              parent.$('#main-div button.searchBtn').trigger("click");  // 触发刷新
              parent.layer.close(mylay);
              return;
            }
            layer.msg('提交失败：' + data, { icon: 0 });
          },
          error: function (ex) {
            layer.msg('提交失败：' + str(ex), { icon: 2 });
          }
        });
        return false;
      });

      // 监听下拉框
      form.on('select(method_change)', function (data) {
        var method_chosen = $('#method_choose').val();
        if (method_chosen && method_chosen.toLowerCase().startsWith('differ.jh.business.')) {
          $('#biz-feild-box').show();
          $('#content-box-title').text('字段内容');
        } else {
          $('#biz-feild-box').hide();
          $('#content-box-title').text('接口返回');
        }
      });

    });

  </script>
</body>

</html>