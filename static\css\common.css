body {
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Aria<PERSON>, sans-serif;
}

.layui-table,
.layui-table-view {
  margin: 5px 0 0 0;
}

.center-body table th {
  font-weight: bold !important;
  text-align: center !important;
}

.center-body {
  min-height: 100%;
  margin: 0px 5px 0px 75px;
}

.item-hide {
  display: none;
}

.layui-table-page div {
  float: right;
}

.layui-nav {
  width: 70px !important;
}

/* 侧边栏容器 */
#sidebar-container {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  width: 70px;
  z-index: 1000;
}

.layui-nav-item>a {
  cursor: pointer;
}

.layui-laypage .layui-laypage-curr .layui-laypage-em {
  background-color: var(--el-button-background-color);
}

.layui-btn+.layui-btn {
    margin-left: 1px;
}

.ml5 {
  margin-left: 5px;
}

.layui-form-label {
  padding-left: 0px;
  padding-right: 5px;
}

.layui-table-cell {
  height: 39px;
}

.form-resubmit {
  float: right;
}

.form-div .layui-form {
  margin: 5px !important;
  float: right;
}

/* 侧边栏底部按钮容器 */
.sidebar-bottom-buttons {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 1001;
}

/* 侧边栏圆形按钮 */
.sidebar-circle-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #FF9800;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid #FF9800;
}

.sidebar-circle-btn:hover {
  background-color: #FF5722;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  border-color: #FF5722;
}

.sidebar-circle-btn i {
  font-size: 18px;
}

/* 主题切换按钮特殊样式 */
.theme-switch-btn {
  background-color: #FFB800;
  border-color: #FFB800;
}

.theme-switch-btn:hover {
  background-color: #FF9800;
  border-color: #FF9800;
}

/* 主界面右侧容器 */
#main-div .layui-form {
  position: relative;
}

/* 右侧浮动按钮容器 */
.right-floating-buttons {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 1001;
}

/* 浮动圆形按钮 */
.floating-circle-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border: 2px solid transparent;
}

.floating-circle-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

.floating-circle-btn i {
  font-size: 20px;
}

/* 新增按钮样式 */
.add-btn {
  background-color: #5FB878;
  border-color: #5FB878;
}

.add-btn:hover {
  background-color: #16B777;
  border-color: #16B777;
}

/* 保存按钮样式 */
.save-btn {
  background-color: #1E9FFF;
  border-color: #1E9FFF;
}

.save-btn:hover {
  background-color: #009688;
  border-color: #009688;
}

/* 深色模式下的按钮样式 */
[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .sidebar-circle-btn {
  background-color: #393D49;
  color: #C2C2C2;
  border: 1px solid #555;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .sidebar-circle-btn:hover {
  background-color: #009688;
  color: white;
  border-color: #009688;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .theme-switch-btn {
  background-color: #5FB878;
  border-color: #5FB878;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .theme-switch-btn:hover {
  background-color: #16B777;
  border-color: #16B777;
}

/* 深色模式下的浮动按钮样式 */
[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .floating-circle-btn {
  border-color: #555;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .add-btn {
  background-color: #5FB878;
  border-color: #5FB878;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .add-btn:hover {
  background-color: #16B777;
  border-color: #16B777;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .save-btn {
  background-color: #1E9FFF;
  border-color: #1E9FFF;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .save-btn:hover {
  background-color: #009688;
  border-color: #009688;
}

.push-notify-form-item {
  width: 300px !important;
}

/* 工具栏样式 */
.toolbar-container {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.toolbar-left {
  display: flex;
  flex: 1;
}

.toolbar-right {
  display: flex;
  justify-content: flex-end;
}

/* 左右分栏布局 */
.split-container {
  display: flex;
  width: 100%;
}

.left-panel {
  width: 60%;
}

.right-panel {
  width: 40%;
  margin: 5px;
}

/* 日志界面特殊样式 */
.record-container {
  width: 100%;
}

/* 说明界面样式 */
#guide-div {
  overflow: auto;
  height: calc(100vh - 40px);
}

#guide-div > div {
  display: flex;
  justify-content: center;
  width: 100%;
}

.markdown-body {
  box-sizing: border-box;
  min-width: 200px;
  max-width: 980px;
  margin: 0 auto;
  padding: 45px;
  background-color: var(--markdown-body-bg-color, #fff);
}

@media (max-width: 767px) {
  .markdown-body {
    padding: 15px;
  }
}

/* 深色模式下的 Markdown 样式 */
[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body {
  --markdown-body-bg-color: #0d1117;
  color: #c9d1d9;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body a {
  color: #58a6ff;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body h1,
[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body h2,
[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body h3,
[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body h4,
[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body h5,
[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body h6 {
  color: #e6edf3;
  border-bottom-color: #21262d;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body hr {
  background-color: #30363d;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body blockquote {
  color: #8b949e;
  border-left-color: #30363d;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body code {
  background-color: rgba(110, 118, 129, 0.4);
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body pre {
  background-color: #161b22;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body table tr {
  background-color: #0d1117;
  border-color: #30363d;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body table tr:nth-child(2n) {
  background-color: #161b22;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body table th,
[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body table td {
  border-color: #30363d;
}

/* 字段映射CRUD界面样式 */
.mapping-dialog-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 15px;
  box-sizing: border-box;
}

.mapping-table-container {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 15px;
  min-height: 0; /* 重要：允许flex子项收缩 */
}

.mapping-save-bar {
  flex-shrink: 0; /* 防止保存栏被压缩 */
  text-align: right;
  padding-top: 15px;
  border-top: 1px solid #e6e6e6;
}

#mapping-table {
  border-collapse: collapse;
  width: 100%;
}

#mapping-table th,
#mapping-table td {
  border: 1px solid #e6e6e6;
  padding: 8px;
  vertical-align: middle;
}

#mapping-table th {
  font-weight: bold;
}

#mapping-table .layui-input {
  border: 1px solid #e6e6e6;
  height: 32px;
  line-height: 32px;
  padding: 0 8px;
  width: 100%;
  box-sizing: border-box;
}

#mapping-table .layui-input:focus {
  border-color: #1E9FFF;
}

/* 操作按钮样式 */
#mapping-table .add-mapping-btn {
  margin-right: 5px;
}

#mapping-table .layui-btn-xs {
  padding: 1px 5px;
  font-size: 12px;
  border-radius: 2px;
}

/* 深色模式下的字段映射样式 */
[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .mapping-save-bar {
  border-top-color: #30363d;
  background-color: #0d1117;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body #mapping-table th,
[href="./static/js/layui/css/layui-theme-dark.css"] ~ body #mapping-table td {
  border-color: #30363d;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body #mapping-table th {
  background-color: #21262d;
  color: #e6edf3;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body #mapping-table .layui-input {
  background-color: #0d1117;
  border-color: #30363d;
  color: #c9d1d9;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body #mapping-table .layui-input:focus {
  border-color: #1E9FFF;
}
