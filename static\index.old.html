<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="Access-Control-Allow-Origin" content="*" />
  <title>模拟网关后台</title>

  <script type="text/javascript" src="./static/js/jquery.min.js"></script>
  <script type="text/javascript" src="./static/js/layui/layui.js"></script>

  <script type="text/javascript" src="./static/js/bignumber.min.js"></script>
  <script type="text/javascript" src="./static/js/jsonlint.js"></script>
  <script type="text/javascript" src="./static/js/jquery.json.js"></script>

  <!--看板娘-->
  <script type="text/javascript" src="./static/js/L2Dwidget.min.js"></script>

  <script>
    L2Dwidget.init({
      "model": { jsonPath: "https://unpkg.com/live2d-widget-model-tororo@1.0.5/assets/tororo.model.json", "scale": 1 },
      "display": { "position": "left", "width": 200, "height": 400, "hOffset": 0.5 }
    });
  </script>

  <link rel="shortcut icon" href="./static/favicon.ico" type="image/x-icon">

  <link rel="stylesheet" href="./static/js/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="./static/js/layui/css/layui-theme-dark.css" id="layui_theme_css">

  <style>
    body {
      font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
    }

    .layui-table,
    .layui-table-view {
      margin: 5px 0 0 0;
    }

    .center-body table th {
      font-weight: bold !important;
      text-align: center !important;
    }

    .center-body {
      min-height: 100%;
      margin: 0px 5px 0px 75px;
    }

    .item-hide {
      display: none;
    }

    .layui-table-page div {
      float: right;
    }

    .layui-nav {
      width: 70px !important;
    }

    .layui-nav-item>a {
      cursor: pointer;
    }

    .layui-laypage .layui-laypage-curr .layui-laypage-em {
      background-color: var(--el-button-background-color);
    }

    .layui-form-label {
      padding-left: 0px;
      padding-right: 5px;
    }

    .layui-table-cell {
      height: 39px;
    }

    .form-resubmit {
      float: right;
    }

    .form-div .layui-form {
      margin: 5px !important;
      float: right;
    }

    .skin-switch {
      position: absolute;
      z-index: 999;
      bottom: 25px;
      left: 10px;
    }

    .push-notify-form-item {
      width: 300px !important;
    }

  </style>

</head>

<body>
  <div>
    <ul class="layui-nav layui-nav-tree layui-nav-side">
      <li class="layui-nav-item layui-this"><a data-link="main" data-role="order">订单</a></li>
      <li class="layui-nav-item"><a data-link="main" data-role="goods">商品</a></li>
      <li class="layui-nav-item"><a data-link="main" data-role="polymsg">消息</a></li>
      <li class="layui-nav-item"><a data-link="main" data-role="common">通用</a></li>
      <li class="layui-nav-item"><a data-link="record" data-role="record">日志</a></li>
    </ul>
  </div>
  <div class="layui-form skin-switch">
    <input type="checkbox" name="CCC" value="2" checked lay-skin="switch" lay-filter="skin-switch" id="skin-switch">
    <div lay-checkbox>
      <i class="layui-icon layui-icon-moon"></i> |
      <i class="layui-icon layui-icon-light"></i>
    </div>
  </div>

  <!-- 模拟日志容器 -->
  <div id="record-div" class="center-body item-hide">
    <!-- 表单 -->
    <div class="form-div" style="width: 100%;">
      <form id="record-menu-box" class="layui-form" action="">

        <div class="layui-inline" id="time_range">
          <label class="layui-form-label">时间选择</label>
          <div class="layui-input-inline">
            <input type="text" name="date-start" id="date-start" readonly=true autocomplete="off" class="layui-input">
          </div>
          <div class="layui-form-mid">-</div>
          <div class="layui-input-inline">
            <input type="text" name="date-end" id="date-end" readonly=true autocomplete="off" class="layui-input">
          </div>
        </div>

        <div class="layui-inline">
          <label class="layui-form-label">平台</label>
          <div class="layui-input-inline">
            <select class="plat_choose" id="record-plat" name="plat" lay-search="">
            </select>
          </div>
        </div>

        <div class="layui-inline">
          <label class="layui-form-label">接口类型</label>
          <div class="layui-input-inline">
            <select id="method" class="method_choose" name="method" lay-search="">
            </select>
          </div>
        </div>

        <div class="layui-inline">
          <label class="layui-form-label">会员名</label>
          <div class="layui-input-inline">
            <input type="text" id="user_name" autocomplete="off" class="layui-input">
          </div>
        </div>

        <div class="layui-inline">
          <label class="layui-form-label">请求id</label>
          <div class="layui-input-inline">
            <input type="tel" id="request_id" autocomplete="off" class="layui-input">
          </div>
        </div>

        <button lay-filter="record-search" type="submit" class="searchBtn layui-btn layui-btn-normal"
          lay-submit="">查询</button>
      </form>
    </div>

    <!-- 表格 -->
    <div style="display: flex; width: 100%;">
      <table id="record-table" lay-filter="record-tool" style="width: 100%;"></table>
    </div>
  </div>
  <!-- 样例容器 -->
  <div id="main-div" class="center-body">

    <div class="form-div" style="width: 100%;">
      <form class="layui-form" action="">
        <div class="layui-inline">
          <label class="layui-form-label">平台</label>
          <div class="layui-input-inline">
            <select class="plat_choose" id="plat_choose" name="plat_choose" lay-search=""></select>
          </div>
          <div class="layui-inline form-item common-tool">
            <label class="layui-form-label">接口类型</label>
            <div class="layui-input-inline">
              <select class="method_choose" id="method_choose" name="method" lay-search=""></select>
            </div>
          </div>
        </div>
        <button lay-filter="main_search" id="main_search" type="submit" class="searchBtn layui-btn layui-btn-normal" lay-submit="">查询</button>
        <button type="button" class="layui-btn layui-btn-normal form-item order-tool" id="order_add">新增</button>
        <button type="button" class="layui-btn layui-btn-normal form-item goods-tool" id="goods_add">新增</button>
        <button type="button" class="layui-btn layui-btn-normal form-item common-tool" id="common_add">新增</button>
        <button type="button" class="layui-btn layui-btn-danger" id="plat_add">新平台</button>
      </form>

    </div>

    <div style="display: flex; width: 100%;">
      <div style="width: 60%;">
        <table id="main-table" lay-filter="main_tool" style="width: 100%;"></table>
      </div>
      <div style="width: 40%; margin: 5px;">
        <form class="layui-form layui-form-pane" id="preview-form" action="" style="width: 100%;">
          <div class="layui-form-item">
            <label class="layui-form-label">平台</label>
            <div class="layui-input-inline">
              <input type="hidden" id="example-id" name="id">
              <input type="hidden" id="example-plat-id" name="plat_id">
              <input type="text" id="example-plat" name="plat" readonly="readonly" autocomplete="off" class="layui-input">
            </div>
          </div>
          <div class="layui-form-item push-notify">
            <label class="layui-form-label">接口</label>
            <div class="layui-input-inline push-notify-form-item">
              <input type="text" id="example-interface" name="plat" readonly="readonly" autocomplete="off"
                class="layui-input">
            </div>
          </div>
          <div class="layui-form-item push-notify">
            <label class="layui-form-label">平台</label>
            <div class="layui-input-inline push-notify-form-item">
              <select class="plat_choose" id="push_plat" lay-search="" lay-filter="push_change"></select>
            </div>
          </div>
          <div class="layui-form-item push-notify">
            <label class="layui-form-label">吉客号</label>
            <div class="layui-input-inline push-notify-form-item">
              <select id="push_user" name="user" lay-search="" lay-filter="push_change" >
                <option value="420001" selected="">420001</option>
                <option value="420002">420002</option>
                <option value="440001">440001</option>
                <option value="450001">450001</option>
                <option value="jackyun_dev">jackyun_dev</option>
              </select>
            </div>
          </div>
          <div class="layui-form-item push-notify">
            <label class="layui-form-label">店铺选择</label>
            <div class="layui-input-inline push-notify-form-item">
              <select id="push_shop" name="shop" lay-search=""></select>
            </div>
            <div class="layui-form-mid" style="padding: 0 !important;"> 
              <!-- <input type="checkbox" checked="" id="auth-shop-only" lay-filter="auth-shop" lay-skin="switch" lay-text="授权店铺|所有店铺"> -->
              <input type="checkbox" checked="" id="auth-shop-only" lay-filter="auth-shop" lay-skin="tag" title="已授权店铺" checked>
              <button type="submit" class="layui-btn" lay-submit="" lay-filter="push-notify">gate推送</button>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">标签</label>
            <div class="layui-input-block">
              <input type="text" id="example-tag" name="tag" autocomplete="off" class="layui-input">
            </div>
          </div>
          <div class="layui-form-item" id="feild-edit-box" style="display: none;">
            <div class="layui-inline">
              <label class="layui-form-label">字段</label>
              <div class="layui-input-block">
                <input type="hidden" id="example-method" name="method">
                <input type="text" name="biz_feild" id="example-feild" autocomplete="off" class="layui-input" value="1">
              </div>
            </div>
          </div>
          <div class="layui-form-item" id="priority-edit-box" style="display: none;">
            <div class="layui-inline">
              <label class="layui-form-label">优先级</label>
              <div class="layui-input-block">
                <input type="text" name="priority" id="example-priority" lay-verify="number" autocomplete="off" class="layui-input" value="1">
              </div>
            </div>
          </div>
          <div class="layui-form-item layui-form-text" id="content-box">
            <label class="layui-form-label" style="text-indent: 25px;">详情</label>
            <div class="layui-input-block">
              <textarea name="content" id="example-content" placeholder="请输入内容" class="layui-textarea"
                style="height: 100%;"></textarea>
            </div>
          </div>
          <div class="layui-form-item">
            <div class="layui-input-block form-resubmit">
              <button type="submit" class="layui-btn" lay-submit="" lay-filter="repost">保存</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>

  <script type="text/html" id="record-bar">
    <a class="layui-btn layui-btn-xs" lay-event="record-detail">查看</a>
  </script>

  <script type="text/html" id="order_bar">
    <a class="layui-btn layui-btn-xs order_push_btn" lay-event="order_push">推送</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="row_del">删除</a>
  </script>

  <script type="text/html" id="goods_bar">
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="row_del">删除</a>
  </script>

  <script type="text/html" id="common_bar">
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="row_del">删除</a>
  </script>

  <script>

    // 平台列表
    var platAll = [{ "PlatValue": "", "Name": "" }];
    // 接口列表
    var apiAll = [{ "apitype": "", "name": "" }];

    var userPlatShop = [];
    // 重新加载会员+平台店铺
    var reloadUserPlatShop = function (platId, userName, shopSelectId) {

      userPlatShop = []
      $("#" + shopSelectId).empty();
      if (!platId || platId == '') {
        return
      }

      var auth_shop_only = $('#auth-shop-only').prop('checked');
      
      $.ajax({
        url: "/shop/getShopByPlat",
        type: 'POST',
        async: false,
        data: { user: userName, plat: platId, auth_shop_only: auth_shop_only },
        dataType: "text",
        success: function (ret) {
          var data = JSON.parse(ret)
          if (data.length > 0) {
            userPlatShop = userPlatShop.concat(data);

            $.each(data, function (i, item) {
              var options = "<option value='" + item.shop_id + "'>" + item.shop_name + "</option>";
              $("#" + shopSelectId).append(options);
            });

            // 重新渲染表单
            layui.form.render();
          }
        },
        error: function (ex) {
          layer.msg('店铺加载失败：' + ex);
        }
      });
    }

    // 格式话表格数据中的plat值
    var formatRowDataPlat = function(rowData) {
      if (!rowData) {
        return;
      }
      rowData.platId = rowData.plat;
      if (rowData.plat == '' || rowData.plat == 0) {
        rowData.plat = '【通用】'
      } else {
        var match = platAll.find(a => a.PlatValue == rowData.plat)
        if (match) {
          rowData.plat = match.Name;
        }
      }
    }
    
    // 各菜单配置
    var menuConfig = {
      'record': {
        url: '/record/get',
        cols: [[ //表头
          { field: 'plat', title: '平台', width: '10%', align: 'center' }
          , { field: 'method', title: '接口类型', width: '16%', align: 'center' }
          , { field: 'user_name', title: '会员名', width: '16%', align: 'center' }
          , { field: 'token', title: 'Token', width: '16%', align: 'center' }
          , { field: 'request_id', title: '请求ID', width: '16%', align: 'center' }
          , { field: 'req_time', title: '请求时间', width: '16%', align: 'center' }
          , { fixed: 'right', title: '详情', width: '10%', align: 'center', toolbar: '#record-bar' }
        ]],
        parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
          for (i in res.data) {
            row = res.data[i]
            var apimatch = apiAll.find(a => a.apitype == row.method)
            if (apimatch) {
              row.method = apimatch.name
            }
            formatRowDataPlat(row);
          }
        }
      },
      'order': {
        url: '/order/get',
        cols: [[ //表头
          { field: 'plat', title: '平台', width: '20%', align: 'center' }
          , { field: 'tag', title: '标签', width: '28%', align: 'center' }
          , { field: 'author_ip', title: '录入人IP', width: '16%', align: 'center' }
          , { field: 'priority', title: '优先级', width: '8%', align: 'center' }
          , { field: 'gmt_modify', title: '更新时间', width: '16%', align: 'center' }
          , { fixed: 'right', title: '操作', width: '12%', align: 'center', toolbar: '#order_bar' }
        ]], 
        parseData: function (res) {
          for (i in res.data) {
            formatRowDataPlat(res.data[i]);
          }
        }
      },
      'goods': {
        url: '/goods/get',
        cols: [[
          , { field: 'plat', title: '平台', width: '22%', align: 'center' }
          , { field: 'tag', title: '标签', width: '38%', align: 'center' }
          , { field: 'author_ip', title: '录入人IP', width: '16%', align: 'center' }
          , { field: 'gmt_modify', title: '更新时间', width: '16%', align: 'center' }
          , { fixed: 'right', title: '操作', width: '8%', align: 'center', toolbar: '#goods_bar' }
        ]], 
        parseData: function (res) {
          for (i in res.data) {
            formatRowDataPlat(res.data[i]);
          }
        }
      },
      'polymsg': {
        url: '/polymsg/getApiByType',
        cols: [[
          , { field: 'name', title: '名称', width: '50%', align: 'center' }
          , { field: 'apitype', title: '接口', width: '50%', align: 'center' }
        ]],
        parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
          for (i in res.data) {
            row = res.data[i];
            row.id = row.apitype;
          }
        }
      },
      'common': {
        url: '/common/get',
        cols: [[
          , { field: 'plat', title: '平台', width: '12%', align: 'center' }
          , { field: 'method', title: '接口', width: '20%', align: 'center' }
          , { field: 'biz_feild', title: '字段名称', width: '10%', align: 'center' }
          , { field: 'tag', title: '标签', width: '25%', align: 'center' }
          , { field: 'author_ip', title: '录入人IP', width: '10%', align: 'center' }
          , { field: 'gmt_modify', title: '更新时间', width: '15%', align: 'center' }
          , { fixed: 'right', title: '操作', width: '8%', align: 'center', toolbar: '#common_bar' }
        ]], 
        parseData: function (res) {
          for (i in res.data) {
            row = res.data[i];
            formatRowDataPlat(row);
            if (row.method) {
              var methodName = row.method.toLowerCase()
              var apimatch = apiAll.find(a => a.apitype.toLowerCase() == methodName)
              if (apimatch) {
                row.method = apimatch.name
              }
            } else {
              console.log(row);
            }
          }
        }
      }
    }

    // 重置详情表单
    var resetDetail = function (menu_role) {
      $("#example-id").val('');
      $('#example-plat').val('');
      $('#example-tag').val('');
      $('#example-feild').val('');
      $('#example-plat-id').val('');
      $('#example-method').val('');
      $('#example-priority').val('');
      $('#example-content').val('');
      $('#example-interface').val('');

      if (menu_role == 'polymsg') {
        $('#preview-form > .layui-form-item').hide();
        $('#preview-form > .push-notify').show();
      } else {
        $('#preview-form > .layui-form-item').show();
        $('#preview-form > .push-notify').hide();

        if (menu_role === 'common') {
          $('#feild-edit-box').show();
          $('#priority-edit-box').hide();
        } else {
          $('#feild-edit-box').hide();
          $('#priority-edit-box').show();
        }
      }

      $('#content-box').show();

    }

    // 格式化json
    var formatJson = function (json_str) {
      try {
        jsonObject = jsonlint.parse(json_str);
        json = JSON.stringify(jsonObject, undefined, 4);
        return json;
      }
      catch (e) {
        console.log('格式化json出错:' + e);
        console.log(json_str);
        return json_str;
      }
    }

    // 获取并检查example-content内的json数据
    var getExampleContent = function (menu_role) {
      var dataContent = $('#example-content').val();
      if (dataContent == '') {
        alertError('请不要输入空数据')
        return null;
      }

      var obj = null;
      // 订单数据格式校验
      try {
        obj = jsonlint.parse(dataContent.toLowerCase());
        if (typeof obj == 'object' && obj) {
          // 订单数据校验
          if (menu_role == 'order') {
            if (Object.prototype.toString.call(obj) === '[object Array]') {
              alertError('多个订单须分多次录入, 不要录入订单列表');
              return null;
            }
            if (!obj.platorderno || !obj.goodinfos) {
              alertError('缺少平台订单号或商品信息, 请检查后重新录入');
              return null;
            }
          }
          // 商品数据校验
          if (menu_role == 'goods') {
            if (Object.prototype.toString.call(obj) === '[object Array]') {
              alertError('多个商品须分多次录入, 不要录入商品列表');
              return null;
            }
            if (!obj.platproductid) {
              alertError('缺少平台商品id, 请检查后重新录入');
              return null;
            }
          }
        } else {
          alertError('数据格式非object, 请检查后重新录入');
          return null;
        }
      } catch (e) {
        alertError('数据格式不正确, 请检查后重新输入');
        console.log(e);
        return null;
      }
      // 压缩
      var t = dataContent.replace(/[\r\n]/g, "")
      for (var n = [], o = !1, i = 0, r = t.length; r > i; i++) {
        var a = t.charAt(i);
        o && a === o ? "\\" !== t.charAt(i - 1) && (o = !1) : o || '"' !== a && "'" !== a ? o || " " !== a && "	" !== a || (a = "") : o = a, n.push(a)
      }
      dataContent = n.join("");
      return dataContent;
    }

    // layer弹出错误信息
    var alertError = function (error_msg) {
      layer.alert(error_msg, { icon: 2, title: '错误' })
    }

    // 表格高度
    var tableHeight = Math.max(document.documentElement.scrollHeight, document.documentElement.clientHeight) - 60;
    var tablineHeight = parseInt(tableHeight / 40 / 5) * 5;

    // 数据初始化
    $(function () {

      // 预设textarea高度
      document.getElementById("example-content").style.height = (tableHeight - 270) + 'px';

      // 导航菜单
      $("ul.layui-nav>li>a").click(function () {

        var link = $(this).data("link");
        var role = $(this).data("role");

        $('.center-body').addClass('item-hide');
        $('#' + link + '-div').removeClass('item-hide');
        $('#' + role + '-menu-box').removeClass('item-hide');

        $('.form-item').addClass('item-hide');
        $('.' + role + '-tool').removeClass('item-hide');

        var menu_config = menuConfig[role];
        if (!menu_config) {
          alert('缺少菜单配置');
          return;
        }

        var table_config = {
          elem: '#' + link + '-table'
          , height: tableHeight
          , url: menu_config.url
          , method: 'post'
          , title: '查询'
          , page: true //开启分页
          , limit: tablineHeight
          , autoSort: false
          , limits: [5, 10, 15, 20, 25, 30, 35, 40, 45, 50]
          , cols: menu_config.cols
          , parseData: menu_config.parseData
          , done: function (res, curr, count) {
            if (count > 0) {
              $('#' + link + '-table').next().find('div.layui-table-body>table>tbody>tr>td>div.layui-table-cell:first').click();
            } else {
              resetDetail(role);
            }
          }
        };

        // 执行一个table 实例并渲染
        layui.table.render(table_config);
        // 模拟点击, 重新请求数据渲染表格
        $('#' + link + '-div button.searchBtn').trigger("click");

      });

      $.fn.autoHeight = function () {
        function autoHeight(elem) {
          elem.style.height = 'auto';
          elem.scrollTop = 0; //防抖动
          elem.style.height = elem.scrollHeight + 10 + 'px';
        }
        this.each(function () {
          autoHeight(this);
        });
      }
      
      // 平台注册
      $('#plat_add').on('click', function () {
        layer.open({
          type: 2,
          title: "平台注册",
          skin: 'layui-layer-rim', //自定义选中色值
          area: ['512px', '240px'], //宽高
          closeBtn: 0, //不显示关闭按钮
          shadeClose: true, //开启遮罩关闭
          content: './static/platAdd.html',
          success: function (layero, index) {
            var iframeWin = window[layero.find('iframe')[0]['name']];
            if ($('#skin-switch').prop('checked')) {
              iframeWin.$('#layui_theme_css').attr('href', './js/layui/css/layui-theme-dark.css')
            } else {
              iframeWin.$('#layui_theme_css').attr('href', '')
            }
          }
        });
      });
      
      // 新增订单样例
      $('#order_add').on('click', function () {
        layer.open({
          type: 2,
          title: "新增订单样例",
          skin: 'layui-layer-rim', //加上边框
          area: ['1024px', '768px'], //宽高
          closeBtn: 0, //不显示关闭按钮
          shadeClose: true, //开启遮罩关闭
          content: './static/orderLibraryAdd.html',
          success: function (layero, index) {
            var iframeWin = window[layero.find('iframe')[0]['name']];
            if ($('#skin-switch').prop('checked')) {
              iframeWin.$('#layui_theme_css').attr('href', './js/layui/css/layui-theme-dark.css')
            } else {
              iframeWin.$('#layui_theme_css').attr('href', '')
            }
          }
        });
      });
      
      // 新增商品
      $('#goods_add').on('click', function () {
        layer.open({
          type: 2,
          title: "新增商品",
          skin: 'layui-layer-rim', //加上边框
          area: ['1024px', '768px'], //宽高
          closeBtn: 0, //不显示关闭按钮
          shadeClose: true, //开启遮罩关闭
          content: './static/goodsLibAdd.html',
          success: function (layero, index) {
            var iframeWin = window[layero.find('iframe')[0]['name']];
            if ($('#skin-switch').prop('checked')) {
              iframeWin.$('#layui_theme_css').attr('href', './js/layui/css/layui-theme-dark.css')
            } else {
              iframeWin.$('#layui_theme_css').attr('href', '')
            }
          }
        });
      });
      
      // 新增通用样例
      $('#common_add').on('click', function () {
        layer.open({
          type: 2,
          title: "新增通用样例",
          skin: 'layui-layer-rim',
          area: ['1024px', '868px'],
          closeBtn: 0,
          shadeClose: true,
          content: './static/commonLibAdd.html',
          success: function (layero, index) {
            var iframeWin = window[layero.find('iframe')[0]['name']];
            if ($('#skin-switch').prop('checked')) {
              iframeWin.$('#layui_theme_css').attr('href', './js/layui/css/layui-theme-dark.css')
            } else {
              iframeWin.$('#layui_theme_css').attr('href', '')
            }
          }
        });
      });

      // 加载平台列表    
      $.ajax({
        url: "/plat/getall",
        type: 'GET',
        dataType: 'json',
        async: false,
        success: function (data) {
          platAll = platAll.concat(data);
          // 填充下拉列表
          $.each(platAll, function (i, item) {
            var optText = '[' + item.PlatValue + ']' + item.Name;
            if (optText == '[]') {
              optText = ''
            }
            var options = "<option value='" + item.PlatValue + "'>" + optText + "</option>";
            $(".plat_choose").append(options);
          });
          // 重新渲染表单
          layui.form.render();
        },
        error: function (ex) {
          console.log(ex)
        }
      });

      // 加载接口列表    
      $.ajax({
        url: "/common/getapi",
        type: 'GET',
        dataType: 'json',
        async: false,
        success: function (data) {
          var apiBusiness = data.find(a => a.type == "Business");
          if (apiBusiness) {
            apiAll = apiAll.concat(apiBusiness.apitypes);
          }
          var apiCommom = data.find(a => a.type == "Commom");
          if (apiCommom) {
            apiAll = apiAll.concat(apiCommom.apitypes);
          }
          if (apiAll.length > 0) {
            $.each(apiAll, function (i, item) {
              var options = "<option value='" + item.apitype + "'>" + item.name + "</option>";
              $(".method_choose").append(options);
            });
            // 重新渲染表单
            layui.form.render();
          }
        },
        error: function (ex) {
          layer.open({
            type: 1,
            area: ['512px', '384px'],
            title: '加载聚合接口列表出错',
            closeBtn: 0,
            shadeClose: true,
            content: ex.responseText
          });
        }
      });
    });

    layui.config({
      version: '1626897823561' //为了更新 js 缓存, 可忽略
    });

    //加载模块  
    layui.use(function () { //亦可加载特定模块：layui.use(['layer', 'laydate', function(){
      // 得到各种内置组件
      var layer = layui.layer //弹层
        , laypage = layui.laypage //分页
        , laydate = layui.laydate //日期
        , table = layui.table //表格
        , element = layui.element //元素操作
        , dropdown = layui.dropdown //下拉菜单
        , carousel = layui.carousel //轮播
        , form = layui.form

      var dateTime = new Date();
      laydate.render({
        elem: '#date-start'
        , type: 'datetime'
        , format: 'yyyy-MM-dd HH:mm'
        , value: new Date(new Date(dateTime.toLocaleDateString()).getTime())
      });
      laydate.render({
        elem: '#date-end'
        , type: 'datetime'
        , format: 'yyyy-MM-dd HH:mm'
        , value: new Date(new Date(dateTime.toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
      });

      // 监听请求日志表单提交
      form.on('submit(record-search)', function (data) {
        table.reload('record-table', {
          where: {
            request_id: $('#request_id').val(),
            time_start: $('#date-start').val(),
            time_end: $('#date-end').val(),
            plat: $('#record-plat').val(),
            api_type: $('#method').val(),
            user_name: $('#user_name').val(),
          }
          , page: { curr: 1 }
        });
        return false;//false：阻止表单跳转 true：表单跳转
      });

      form.on('submit(main_search)', function (data) {
        var menu_role = $("li.layui-this>a").data("role");
        var menu_config = menuConfig[menu_role];
        if (!menu_config) {
          alert('缺少菜单配置');
          return;
        }
        table.reload('main-table', {
          url: menu_config.url
          , where: {
            plat: $('#plat_choose').val(),
            method: $('#method_choose').val()
          }
          , page: { curr: 1 }
        });
        return false;//false：阻止表单跳转 true：表单跳转
      });

      table.on('row(main_tool)', function (obj) {
        // console.log(obj.tr) //得到当前行元素对象
        // console.log(obj.data) //得到当前行数据

        $(".layui-table-body tr").attr({ "style": "-webkit-tap-highlight-color" });//其他tr恢复原样
        $(obj.tr).attr({ "style": "background:#409EFF;color:#fff" });//改变当前tr颜色

        var menu_role = $("li.layui-this>a").data("role");

        resetDetail(menu_role);

        var post_url = "/" + menu_role + "/getById";
        var post_data = {
          id: obj.data.id
        };
        // 查询实时数据
        $.ajax({
          url: post_url,
          type: 'POST',
          async: false,
          data: post_data,
          dataType: "json",
          success: function (result) {
            var data = null;
            if (result && result.data) {
              if (Object.prototype.toString.call(result.data) === '[object Array]') {
                if (result.data.length == 0) {
                  layer.msg('查询失败：' + result);
                  return;
                }
                data = result.data[0];
              } else {
                data = result.data;
              }

              if (menu_role == 'polymsg') {
                $('#example-interface').val(obj.data.apitype);
              } else {
                $('#example-plat').val(obj.data.plat);
                $("#example-id").val(data.id);
                $('#example-tag').val(data.tag);
                $('#example-feild').val(data.biz_feild);
                $('#example-plat-id').val(data.plat);
                $('#example-method').val(data.method);
                $('#example-priority').val(data.priority ?? 1);
              }
              $('#example-content').val(formatJson(data.content));

              return;
            }
            layer.msg('查询失败：' + result);
            console.log(result)
          },
          error: function (ex) {
            layer.msg('查询失败：' + str(ex));
            console.log(ex)
          }
        });
      });

      form.on('submit(repost)', function (obj) {

        var menu_role = $("li.layui-this>a").data("role");

        var dataContent = getExampleContent(menu_role);
        if (dataContent == null) {
          return false;
        }

        var post_url = "/" + menu_role + "/update";
        var post_data = {
          id: $("#example-id").val(),
          plat: $('#example-plat-id').val(),
          tag: $('#example-tag').val(),
          biz_feild: $('#example-feild').val(),
          method: $('#example-method').val(),
          priority: $('#example-priority').val() ?? 1,
          content: dataContent
        };

        // 提交   
        $.ajax({
          url: post_url,
          type: 'POST',
          async: false,
          data: post_data,
          dataType: "text",
          success: function (data) {
            var msg = '已提交';
            var icon = 1; // 0: i; 1: √ ; 2: x; 3: ?; 4: 锁; 5: sad; 6: happy
            if (data != 'success') {
              msg += ', ' + data;
              icon = 0;
            }
            layer.msg(msg, { icon: icon });
            $("#main_search").trigger("click");  // 触发刷新
          },
          error: function (ex) {
            layer.msg('提交失败：' + str(ex), { icon: 2 });
            console.log(ex)
          }
        });

        return false;
      });

      // 监听下拉框
      form.on('select(push_change)', function (data) {
        var push_plat = $('#push_plat').val();
        var push_user = $('#push_user').val();
        reloadUserPlatShop(push_plat, push_user, 'push_shop');
      });
      form.on('checkbox(auth-shop)', function (data) {
        var push_plat = $('#push_plat').val();
        var push_user = $('#push_user').val();
        reloadUserPlatShop(push_plat, push_user, 'push_shop');
      });

      // 消息推送
      form.on('submit(push-notify)', function (obj) {

        var push_plat = $('#push_plat').val();
        if (!push_plat || push_plat == '') {
          alertError('请选择平台')
          return false;
        }

        var menu_role = $("li.layui-this>a").data("role");

        var dataContent = getExampleContent(menu_role);
        if (dataContent == null) {
          return false;
        }

        var shopId = $("#push_shop").val();
        var plat = $('#push_plat').val();
        var user = $('#push_user').val();
        var method = $('#example-interface').val();

        var shop_token = '';
        var shopMatch = userPlatShop.filter(x=>x.shop_id === shopId);
        if (shopMatch && shopMatch.length > 0) {
          shop_token = shopMatch[0].poly_token;
        }

        var index = layer.load(0, {shade: false});
        $.ajax({
          url: '/order/pushNotice',
          type: 'POST',
          async: false,
          data: { platid: plat, user: user, shopid: shopId, shopToken: shop_token, messageType: method, msgcontent: dataContent },
          dataType: "text",
          success: function (data) {
            // 关闭 loading
            layer.close(index);
            // 弹窗展示结果
            var rep = formatJson(data);
            layer.open({
              type: 1,
              title: "消息推送返回",
              area: ['524px', '600px'],
              shadeClose: true,
              content: '<textarea style="width: 500px;height: 528px; margin: 8px;padding-left: 5px;border: 1px solid #F0F0F0;">' + rep + '</textarea>'
            });
          },
          error: function (ex) {
            layer.close(index);
            layer.msg('提交失败：' + str(ex), { icon: 2 });
          }
        });

        return false;

      });

      // 请求日志工具
      table.on('tool(record-tool)', function (obj) { //注：tool 是工具条事件名, record-tool 是 table 原始容器的属性 lay-filter="对应的值"
        var data = obj.data //获得当前行数据
          , layEvent = obj.event; //获得 lay-event 对应的值
        if (layEvent === 'record-detail') {
          window.open("/log?id=" + data.request_id);
        }
      });

      // 订单工具事件
      table.on('tool(main_tool)', function (obj) { //注：tool 是工具条事件名, main_tool 是 table 原始容器的属性 lay-filter="对应的值"
        var data = obj.data //获得当前行数据
          , layEvent = obj.event; //获得 lay-event 对应的值
        if (layEvent === 'order_detail') {
          layer.open({
            skin: 'layui-layer-rim',
            type: 2,
            area: ['1024px', '768px'],
            title: '订单详情',
            closeBtn: 0, //不显示关闭按钮
            shadeClose: true, //开启遮罩关闭
            content: './static/orderLibraryView.html',
            success: function (layero, index) {
              var body = layer.getChildFrame('body', index); // 少了这个是不能从父页面向子页面传值的
              // 获取子页面的元素, 进行数据渲染
              body.contents().find("#order-id").val(data.id);
              body.contents().find('#order-plat').val(data.plat);
              body.contents().find('#order-tag').val(data.tag);
              body.contents().find('#order-priority').val(data.priority);
              // 格式化显示
              json = formatJson(data.content);
              body.contents().find('#order-content').val(json);
            }
          });
        }
        if (layEvent === 'order_push') {
          layer.open({
            skin: 'layui-layer-rim',
            type: 2,
            area: ['524px', '600px'],
            title: '订单推送',
            shadeClose: true,
            content: './static/orderPush.html',
            success: function (layero, index) {
              var body = layer.getChildFrame('body', index);
              // 获取子页面的元素, 进行数据渲染
              body.contents().find("#plat").val(data.platId);
              body.contents().find('#order_Id').val(data.id);

              var iframeWin = window[layero.find('iframe')[0]['name']]; //得到iframe页的窗口对象
              iframeWin.loadShop();

              if ($('#skin-switch').prop('checked')) {
                iframeWin.$('#layui_theme_css').attr('href', './js/layui/css/layui-theme-dark.css')
              } else {
                iframeWin.$('#layui_theme_css').attr('href', '')
              }
            }
          });
        }
        if (layEvent === 'goods_detail') {
          layer.open({
            skin: 'layui-layer-rim',
            type: 2,
            area: ['1024px', '768px'],
            title: '商品详情',
            closeBtn: 0,
            shadeClose: true,
            content: './static/goodsLibView.html',
            success: function (layero, index) {
              var body = layer.getChildFrame('body', index);
              // 获取子页面的元素, 进行数据渲染
              body.contents().find("#goods-id").val(data.id);
              body.contents().find('#goods-plat').val(data.plat);
              body.contents().find('#goods-tag').val(data.tag);
              // 格式化显示
              json = formatJson(data.content);
              body.contents().find('#goods-content').val(json);
            }
          });
        }

        if (layEvent === 'row_del') {
          layer.confirm('确认要删除该记录?', {
            btn: ['确认', '取消'] //按钮
          }, function () {

            var link = $(this).data("link");
            var menu_role = $("li.layui-this>a").data("role");
            var post_url = "/" + menu_role + "/del";

            $.ajax({
              url: post_url,
              type: 'POST',
              async: false,
              data: { id: data.id },
              dataType: "text",
              success: function (data) {
                if (data == 'success') {
                  parent.layer.msg('已删除', { icon: 1 });
                  $('#main-div button.searchBtn').trigger("click");
                  return;
                }
                layer.msg('提交失败：' + data, { icon: 0 });
                console.log(data)
              },
              error: function (ex) {
                layer.msg('提交失败：' + str(ex), { icon: 2 });
                console.log(ex)
              }
            });
          });
        }

      });

      form.on('switch(skin-switch)', function (data) {

        var jsonPath = ''

        if ($('#skin-switch').prop('checked')) {
          // 设置为深色主题
          document.getElementById('layui_theme_css').setAttribute('href', './static/js/layui/css/layui-theme-dark.css')
          jsonPath = "https://unpkg.com/live2d-widget-model-tororo@1.0.5/assets/tororo.model.json";
        } else {
          // 恢复浅色主题
          document.getElementById('layui_theme_css').removeAttribute('href')
          jsonPath = "https://unpkg.com/live2d-widget-model-hijiki@1.0.5/assets/hijiki.model.json";
        }

        window.L2Dwidget.config.model.jsonPath = jsonPath;
        window.L2Dwidget.init();

      });

      // 初始化点击
      $("li.layui-this>a").trigger("click");

      //黑猫咪：https://unpkg.com/live2d-widget-model-hijiki@1.0.5/assets/hijiki.model.json
      //白猫咪：https://unpkg.com/live2d-widget-model-tororo@1.0.5/assets/tororo.model.json
      //萌娘：https://unpkg.com/live2d-widget-model-shizuku@1.0.5/assets/shizuku.model.json
      //狗狗：https://unpkg.com/live2d-widget-model-wanko@1.0.5/assets/wanko.model.json
      //萌妹1号：https://unpkg.com/live2d-widget-model-z16@1.0.5/assets/z16.model.json
      //萌妹2号：https://unpkg.com/live2d-widget-model-koharu@1.0.5/assets/koharu.model.json
      //萌妹3号：https://unpkg.com/live2d-widget-model-hibiki@1.0.5/assets/hibiki.model.json
      //妹子4号：https://unpkg.com/live2d-widget-model-izumi@1.0.5/assets/izumi.model.json
      //妹子5号：https://unpkg.com/live2d-widget-model-miku@1.0.5/assets/miku.model.json
      //6号：https://unpkg.com/live2d-widget-model-nico@1.0.5/assets/nico.model.json
      //7号：https://unpkg.com/live2d-widget-model-ni-j@1.0.5/assets/ni-j.model.json
      //8号：https://unpkg.com/live2d-widget-model-nipsilon@1.0.5/assets/nipsilon.model.json
      //9号：https://unpkg.com/live2d-widget-model-nito@1.0.5/assets/nito.model.json
      //10号：https://unpkg.com/live2d-widget-model-tsumiki@1.0.5/assets/tsumiki.model.json
      //11号：https://unpkg.com/live2d-widget-model-unitychan@1.0.5/assets/unitychan.model.json
      //帅哥1号：https://unpkg.com/live2d-widget-model-chitose@1.0.5/assets/chitose.model.json
      //帅哥2号：https://unpkg.com/live2d-widget-model-haruto@1.0.5/assets/haruto.model.json
    });

  </script>
</body>

</html>