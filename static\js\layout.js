// 布局组件
(function() {

  var moduleDic = {
    'order' : {
      name : "订单",
      page : './static/orderLibraryAdd.html'
    },
    'goods' : {
      name : "商品",
      page : './static/goodsLibAdd.html'
    },
    'common' : {
      name : "通用",
      page : './static/commonLibAdd.html'
    },
  }

  // 初始化主界面布局
  function initMainLayout() {
    $('#main-div').append(mainLayoutTemplate);
    // 绑定浮动按钮事件
    bindFloatingButtonEvents();
  }

  // 绑定浮动按钮事件
  function bindFloatingButtonEvents() {
    // 保存按钮事件
    $('#floating-save-btn').on('click', function() {
      // 创建一个隐藏的提交按钮来触发表单提交
      var hiddenSubmit = $('<button type="submit" lay-submit lay-filter="repost" style="display:none;"></button>');
      $('#preview-form').append(hiddenSubmit);
      hiddenSubmit.click();
      hiddenSubmit.remove();
    });

    // 新增按钮事件 - 根据当前模块调用相应的新增功能
    $('#floating-add-btn').on('click', function() {
      var currentRole = $("li.layui-this>a").data("role");

      var moduleData = moduleDic[currentRole];
      if (!moduleData) {
        return;
      }

      layer.open({
        type: 2,
        title: "新增" + moduleData.name + '样例',
        area: ['1024px', '768px'],
        maxmin: true,     // 允许全屏最小化
        anim: 0,          // 0-6 的动画形式，-1 不开启
        content: moduleData.page,
        success: function (layero) {
          applyThemeToIframe(layero);
        }
      });

    });
  }

  // 初始化日志界面布局
  function initRecordLayout() {
    $('#record-div').append(recordLayoutTemplate);
  }

  // 初始化说明界面布局
  function initGuideLayout() {
    $('#guide-div').append(guideLayoutTemplate);
  }

  // 暴露公共方法
  window.LayoutComponent = {
    initMainLayout: initMainLayout,
    initRecordLayout: initRecordLayout,
    initGuideLayout: initGuideLayout
  };
})();
