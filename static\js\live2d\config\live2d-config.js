// Live2D配置
L2Dwidget.init({
  "model": { jsonPath: "https://unpkg.com/live2d-widget-model-tororo@1.0.5/assets/tororo.model.json", "scale": 1 },
  "display": { "position": "left", "width": 200, "height": 400, "hOffset": 0.5 }
});

// 可用的Live2D模型列表
// 黑猫咪：https://unpkg.com/live2d-widget-model-hijiki@1.0.5/assets/hijiki.model.json
// 白猫咪：https://unpkg.com/live2d-widget-model-tororo@1.0.5/assets/tororo.model.json
// 萌娘：https://unpkg.com/live2d-widget-model-shizuku@1.0.5/assets/shizuku.model.json
// 狗狗：https://unpkg.com/live2d-widget-model-wanko@1.0.5/assets/wanko.model.json
// 萌妹1号：https://unpkg.com/live2d-widget-model-z16@1.0.5/assets/z16.model.json
// 萌妹2号：https://unpkg.com/live2d-widget-model-koharu@1.0.5/assets/koharu.model.json
// 萌妹3号：https://unpkg.com/live2d-widget-model-hibiki@1.0.5/assets/hibiki.model.json
// 妹子4号：https://unpkg.com/live2d-widget-model-izumi@1.0.5/assets/izumi.model.json
// 妹子5号：https://unpkg.com/live2d-widget-model-miku@1.0.5/assets/miku.model.json
// 6号：https://unpkg.com/live2d-widget-model-nico@1.0.5/assets/nico.model.json
// 7号：https://unpkg.com/live2d-widget-model-ni-j@1.0.5/assets/ni-j.model.json
// 8号：https://unpkg.com/live2d-widget-model-nipsilon@1.0.5/assets/nipsilon.model.json
// 9号：https://unpkg.com/live2d-widget-model-nito@1.0.5/assets/nito.model.json
// 10号：https://unpkg.com/live2d-widget-model-tsumiki@1.0.5/assets/tsumiki.model.json
// 11号：https://unpkg.com/live2d-widget-model-unitychan@1.0.5/assets/unitychan.model.json
// 帅哥1号：https://unpkg.com/live2d-widget-model-chitose@1.0.5/assets/chitose.model.json
// 帅哥2号：https://unpkg.com/live2d-widget-model-haruto@1.0.5/assets/haruto.model.json
