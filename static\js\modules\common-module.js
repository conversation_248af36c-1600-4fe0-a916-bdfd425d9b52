// 通用模块
(function() {
  // 通用工具栏模板
  const commonToolbarTemplate = `
  <div class="form-div" style="width: 100%;">
    <form class="layui-form" action="">
      <span id="search-container"></span>
      <span id="module-buttons-container"></span>
    </form>
  </div>
  `;

  // 初始化通用模块
  function init() {
    // 注册通用菜单配置
    registerMenuConfig();

    // 绑定通用相关事件
    bindEvents();
  }

  // 注册通用菜单配置
  function registerMenuConfig() {
    menuConfig['common'] = {
      url: '/common/get',
      cols: [[
        { field: 'plat', title: '平台', width: 180, align: 'center' }
        , { field: 'method', title: '接口', width: 200, align: 'center' }
        , { field: 'biz_feild', title: '字段名称', width: 130, align: 'center' }
        , { field: 'tag', title: '标签', cellMinWidth: 200, align: 'center' }
        , { field: 'author_ip', title: '录入人IP', width: 120, align: 'center' }
        , { field: 'gmt_modify', title: '更新时间', width: 166, align: 'center' }
        , { fixed: 'right', title: '操作', width: 65, align: 'center', toolbar:
          `<script type="text/html">
              {{# if(d.author_ip === window.currentUserIP) { }}
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="row_del" title="删除">
                  <i class="layui-icon layui-icon-close"></i>
                </a>
              {{# } else { }}
                <span class="layui-btn layui-btn-xs" style="background-color: #5FB878; color: white; border-color: #5FB878;" title="无权限删除">
                  <i class="layui-icon layui-icon-password"></i>
                </span>
              {{# } }}
            </script>`
          }
      ]],
      parseData: function (res) {
        for (var i in res.data) {
          var row = res.data[i];
          formatRowDataPlat(row);
          if (row.method) {
            var methodName = row.method.toLowerCase()
            var apimatch = apiAll.find(a => a.apitype.toLowerCase() == methodName)
            if (apimatch) {
              row.method = apimatch.name
            }
          } else {
            console.log(row);
          }
        }
      }
    };
  }

  // 绑定通用相关事件
  function bindEvents() {
    // 通用表格工具事件已经在sidebar.js中绑定
    console.log('通用模块初始化完成');

    // 监听表单提交
    layui.form.on('submit(repost)', function (obj) {
      var menu_role = $("li.layui-this>a").data("role");

      var dataContent = getExampleContent(menu_role);
      if (dataContent == null) {
        return false;
      }

      var post_url = "/" + menu_role + "/update";
      var post_data = {
        id: $("#example-id").val(),
        plat: $('#example-plat-id').val(),
        tag: $('#example-tag').val(),
        biz_feild: $('#example-feild').val(),
        method: $('#example-method').val(),
        priority: $('#example-priority').val() ?? 1,
        content: dataContent
      };

      // 提交
      $.ajax({
        url: post_url,
        type: 'POST',
        async: false,
        data: post_data,
        dataType: "text",
        success: function (data) {
          // 更新成功
          if (data == 'success') {
            layer.msg('已提交', { icon: 1 });
            $("#main_search").trigger("click");  // 触发刷新
            return;
          }
          // icon 0: i; 1: √ ; 2: x; 3: ?; 4: 锁; 5: sad; 6: happy
          var msg = '提交失败: ' + data;
          layer.msg(msg, { icon: 0 });
        },
        error: function (ex) {
          layer.msg('提交失败：' + str(ex), { icon: 2 });
          console.log(ex)
        }
      });

      return false;
    });

    // 监听主界面搜索
    layui.form.on('submit(main_search)', function (data) {
      var menu_role = $("li.layui-this>a").data("role");
      var menu_config = menuConfig[menu_role];
      if (!menu_config) {
        alert('缺少菜单配置');
        return;
      }
      var searchParams = {
        plat: $('#plat_choose').val() || ''
      };

      // 只有通用模块需要接口类型参数
      if (menu_role === 'common' && $('#method_choose').length > 0) {
        searchParams.method = $('#method_choose').val() || '';
      }

      layui.table.reload('main-table', {
        url: menu_config.url
        , where: searchParams
        , page: { curr: 1 }
      });
      return false;//false：阻止表单跳转 true：表单跳转
    });

  }

  // 通用数据校验
  function validateData(obj) {
    // 通用模块不需要特殊的数据校验
    return true;
  }

  // 生成工具栏
  function generateToolbar() {
    // 1. 生成搜索区域
    $('#search-container').append(searchAreaTemplate);

    // 重新填充平台下拉框
    $(".plat_choose").empty();
    $.each(platAll, function (_, item) {
      var optText = '[' + item.PlatValue + ']' + item.Name;
      if (optText == '[]') {
        optText = '';
      }
      var options = "<option value='" + item.PlatValue + "'>" + optText + "</option>";
      $(".plat_choose").append(options);
    });

    // 2. 生成接口类型下拉框
    $('#method-container').append(methodSelectTemplate);

    // 重新填充接口类型下拉框
    $(".method_choose").empty();
    $.each(apiAll, function (_, item) {
      var options = "<option value='" + item.apitype + "'>" + item.name + "</option>";
      $(".method_choose").append(options);
    });

    $('#floating-func-box').show();

    // 3. 生成按钮
    // var commonAddButton = $('<button type="button" class="layui-btn layui-btn-normal" id="common_add">新增</button>');
    // $('#module-buttons-container').append(commonAddButton);

    // 绑定新增通用样例按钮事件
    // $('#common_add').on('click', function () {
    //   layer.open({
    //     type: 2,
    //     title: "新增通用样例",
    //     area: ['1024px', '868px'],
    //     maxmin: true,     // 允许全屏最小化
    //     anim: 0,          // 0-6 的动画形式，-1 不开启
    //     content: './static/commonLibAdd.html',
    //     success: function (layero) {
    //       applyThemeToIframe(layero);
    //     }
    //   });
    // });

    // 重新渲染表单
    layui.form.render();
  }

  // 暴露公共方法
  window.CommonModule = {
    init: init,
    validateData: validateData,
    generateToolbar: generateToolbar,
    commonToolbarTemplate: commonToolbarTemplate
  };
})();
