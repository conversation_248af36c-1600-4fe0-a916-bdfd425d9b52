// 商品模块
(function() {

  // 初始化商品模块
  function init() {
    // 注册商品菜单配置
    registerMenuConfig();

    // 绑定商品相关事件
    bindEvents();
  }

  // 注册商品菜单配置
  function registerMenuConfig() {
    menuConfig['goods'] = {
      url: '/goods/get',
      cols: [[
        { field: 'plat', title: '平台', width: 180, align: 'center' }
        , { field: 'tag', title: '标签', cellMinWidth: 200, align: 'center' }
        , { field: 'author_ip', title: '录入人IP', width: 120, align: 'center' }
        , { field: 'gmt_modify', title: '更新时间', width: 166, align: 'center' }
        , { fixed: 'right', title: '操作', width: 65, align: 'center', toolbar:
            `<script type="text/html">
              <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="row_del">删除</a>
            </script>`
          }
      ]],
      parseData: function (res) {
        for (var i in res.data) {
          formatRowDataPlat(res.data[i]);
        }
      }
    };
  }

  // 绑定商品相关事件
  function bindEvents() {
    // 商品表格工具事件已经在sidebar.js中绑定
    console.log('商品模块初始化完成');
  }



  // 商品数据校验
  function validateData(obj) {
    // 检查是否为数组
    if (Object.prototype.toString.call(obj) === '[object Array]') {
      alertError('多个商品须分多次录入, 不要录入商品列表');
      return false;
    }

    // 检查必要字段
    if (!obj.platproductid) {
      alertError('缺少平台商品id, 请检查后重新录入');
      return false;
    }

    return true;
  }

  // 生成工具栏
  function generateToolbar() {
    // 1. 生成搜索区域
    $('#search-container').append(searchAreaTemplate);

    // 重新填充平台下拉框
    $(".plat_choose").empty();
    $.each(platAll, function (_, item) {
      var optText = '[' + item.PlatValue + ']' + item.Name;
      if (optText == '[]') {
        optText = '';
      }
      var options = "<option value='" + item.PlatValue + "'>" + optText + "</option>";
      $(".plat_choose").append(options);
    });

    $('#floating-func-box').show();

    // // 2. 生成按钮
    // var goodsAddButton = $('<button type="button" class="layui-btn layui-btn-normal" id="goods_add">新增</button>');
    // $('#module-buttons-container').append(goodsAddButton);

    // // 绑定新增商品按钮事件
    // $('#goods_add').on('click', function () {
    //   layer.open({
    //     type: 2,
    //     title: "新增商品",
    //     area: ['1024px', '768px'],
    //     maxmin: true,     // 允许全屏最小化
    //     anim: 0,          // 0-6 的动画形式，-1 不开启
    //     content: './static/goodsLibAdd.html',
    //     success: function (layero) {
    //       applyThemeToIframe(layero);
    //     }
    //   });
    // });

    // 重新渲染表单
    layui.form.render();
  }

  // 暴露公共方法
  window.GoodsModule = {
    init: init,
    validateData: validateData,
    generateToolbar: generateToolbar
  };
})();
