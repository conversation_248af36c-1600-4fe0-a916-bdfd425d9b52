// 说明模块
(function() {
  // 初始化说明模块
  function init() {
    // 注册说明菜单配置
    registerMenuConfig();
  }

  // 注册说明菜单配置
  function registerMenuConfig() {
    // 注册说明菜单配置
    menuConfig['guide'] = {
      title: '使用说明',
      url: '',
      cols: []
    };
  }

  // 生成工具栏
  function generateToolbar() {
    // 说明模块不需要工具栏
  }

  // 加载说明文档
  function loadGuideContent() {
    // 使用fetch API加载guide.md文件
    fetch('./static/doc/guide.md')
      .then(response => {
        if (!response.ok) {
          throw new Error('无法加载说明文档: ' + response.status + ' ' + response.statusText);
        }
        return response.text();
      })
      .then(text => {
        // 使用marked.js将markdown转换为HTML
        const html = marked.parse(text);
        // 将HTML插入到页面中
        $('#guide-content').html(html);
      })
      .catch(error => {
        console.error('加载说明文档失败:', error);
        $('#guide-content').html(`
          <div class="layui-card">
            <div class="layui-card-header">错误</div>
            <div class="layui-card-body">
              <p>加载说明文档失败: ${error.message}</p>
              <p>请确保文件 <code>./static/doc/guide.md</code> 存在并且可以访问。</p>
            </div>
          </div>
        `);
      });
  }

  // 说明数据校验
  function validateData(obj) {
    // 说明模块不需要数据校验
    return true;
  }

  // 暴露公共方法
  window.GuideModule = {
    init: init,
    validateData: validateData,
    generateToolbar: generateToolbar,
    loadGuideContent: loadGuideContent
  };
})();
