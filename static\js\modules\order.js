// 订单模块
(function() {

  // 初始化订单模块
  function init() {
    // 注册订单菜单配置
    registerMenuConfig();

    // 绑定订单相关事件
    bindEvents();
  }

  // 注册订单菜单配置
  function registerMenuConfig() {
    menuConfig['order'] = {
      url: '/order/get',
      cols: [[ //表头
        { field: 'plat', title: '平台', width: 180, align: 'center' }
        , { field: 'tag', title: '标签', cellMinWidth: 200, align: 'center' }
        , { field: 'priority', title: '优先级', width: 75, align: 'center' }
        , { field: 'author_ip', title: '录入人IP', width: 120, align: 'center' }
        , { field: 'gmt_modify', title: '更新时间', width: 166, align: 'center' }
        , { fixed: 'right', title: '操作', width: 150, align: 'center', toolbar:
            `<script type="text/html">
              <a class="layui-btn layui-btn-xs" lay-event="order_push">推送</a>
              <a class="layui-btn layui-btn-xs layui-bg-blue" lay-event="openapi_push">管家</a>
              {{# if(d.author_ip === window.currentUserIP) { }}
                <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="row_del">删除</a>
              {{# } else { }}
                <i class="layui-icon layui-icon-password" style="color: #999; font-size: 16px;" title="无权限删除"></i>
              {{# } }}
            </script>`
          }
      ]],
      parseData: function (res) {
        for (var i in res.data) {
          formatRowDataPlat(res.data[i]);
        }
      }
    };
  }

  // 绑定订单相关事件
  function bindEvents() {
    // 订单表格工具事件已经在sidebar.js中绑定
    console.log('订单模块初始化完成');

    // 监听表单提交
    layui.form.on('submit(openapi_push)', function (obj) {
      
      layer.open({
        skin: 'layui-layer-rim',
        type: 2,
        area: ['550px', '660px'],
        title: '管家订单推送',
        shadeClose: true,
        content: './static/openApiOrderPush.html',
        success: function (layero, index) {

          var order_id = $("#example-id").val();
          var plat_id = $('#example-plat-id').val();

          var body = layer.getChildFrame('body', index);
          body.contents().find('#order_Id').val(order_id);
          body.contents().find("#plat").val(plat_id);

          applyThemeToIframe(layero);
        }
      });

      return false;

    });

  }



  // 订单数据校验
  function validateData(obj) {
    // 检查是否为数组
    if (Object.prototype.toString.call(obj) === '[object Array]') {
      alertError('多个订单须分多次录入, 不要录入订单列表');
      return false;
    }

    // 检查必要字段
    if (!obj.platorderno || !obj.goodinfos) {
      alertError('缺少平台订单号或商品信息, 请检查后重新录入');
      return false;
    }

    return true;
  }

  // 生成工具栏
  function generateToolbar() {
    // 1. 生成搜索区域
    $('#search-container').append(searchAreaTemplate);

    // 重新填充平台下拉框
    $(".plat_choose").empty();
    $.each(platAll, function (_, item) {
      var optText = '[' + item.PlatValue + ']' + item.Name;
      if (optText == '[]') {
        optText = '';
      }
      var options = "<option value='" + item.PlatValue + "'>" + optText + "</option>";
      $(".plat_choose").append(options);
    });

    $('#floating-func-box').show();

    // 2. 生成按钮
    // var orderAddButton = $('<button type="button" class="layui-btn layui-btn-normal" id="order_add">新增</button>');
    // $('#module-buttons-container').append(orderAddButton);

    // // 绑定新增订单按钮事件
    // $('#order_add').on('click', function () {
    //   layer.open({
    //     type: 2,
    //     title: "新增订单样例",
    //     area: ['1024px', '768px'],
    //     maxmin: true,     // 允许全屏最小化
    //     anim: 0,          // 0-6 的动画形式，-1 不开启
    //     content: './static/orderLibraryAdd.html',
    //     success: function (layero) {
    //       applyThemeToIframe(layero);
    //     }
    //   });
    // });

    // 重新渲染表单
    layui.form.render();
  }

  // 暴露公共方法
  window.OrderModule = {
    init: init,
    validateData: validateData,
    generateToolbar: generateToolbar
  };
})();
