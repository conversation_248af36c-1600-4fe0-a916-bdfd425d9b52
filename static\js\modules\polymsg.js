// 消息模块
(function() {
  // 初始化消息模块
  function init() {
    // 注册消息菜单配置
    registerMenuConfig();

    // 绑定消息相关事件
    bindEvents();
  }

  // 注册消息菜单配置
  function registerMenuConfig() {
    menuConfig['polymsg'] = {
      url: '/polymsg/getApiByType',
      cols: [[
        { field: 'name', title: '名称', width: '50%', align: 'center' }
        , { field: 'apitype', title: '接口', width: '50%', align: 'center' }
      ]],
      parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
        for (var i in res.data) {
          var row = res.data[i];
          row.id = row.apitype;
        }
      }
    };
  }

  // 绑定消息相关事件
  function bindEvents() {
    // 消息推送
    layui.form.on('submit(push-notify)', function (obj) {
      var push_plat = $('#push_plat').val();
      if (!push_plat || push_plat == '') {
        alertError('请选择平台')
        return false;
      }

      var menu_role = $("li.layui-this>a").data("role");

      var dataContent = getExampleContent(menu_role);
      if (dataContent == null) {
        return false;
      }

      var shopId = $("#push_shop").val();
      var plat = $('#push_plat').val();
      var user = $('#push_user').val();
      var method = $('#example-interface').val();

      var shop_token = '';
      var shopMatch = userPlatShop.filter(x=>x.shop_id === shopId);
      if (shopMatch && shopMatch.length > 0) {
        shop_token = shopMatch[0].poly_token;
      }

      var index = layer.load(0, {shade: false});
      $.ajax({
        url: '/order/pushNotice',
        type: 'POST',
        async: false,
        data: { platid: plat, user: user, shopid: shopId, shopToken: shop_token, messageType: method, msgcontent: dataContent },
        dataType: "text",
        success: function (data) {
          // 关闭 loading
          layer.close(index);
          // 弹窗展示结果
          var rep = formatJson(data);
          layer.open({
            type: 1,
            title: "消息推送返回",
            area: ['524px', '600px'],
            shadeClose: true,
            content: '<textarea style="width: 500px;height: 528px; margin: 8px;padding-left: 5px;border: 1px solid #F0F0F0;">' + rep + '</textarea>'
          });
        },
        error: function (ex) {
          layer.close(index);
          layer.msg('提交失败：' + str(ex), { icon: 2 });
        }
      });

      return false;
    });

    // 监听下拉框
    layui.form.on('select(push_change)', function (data) {
      var push_plat = $('#push_plat').val();
      var push_user = $('#push_user').val();
      reloadUserPlatShop(push_plat, push_user, 'push_shop');
    });

    layui.form.on('checkbox(auth-shop)', function (data) {
      var push_plat = $('#push_plat').val();
      var push_user = $('#push_user').val();
      reloadUserPlatShop(push_plat, push_user, 'push_shop');
    });

  }

  // 消息数据校验
  function validateData(obj) {
    // 消息模块不需要特殊的数据校验
    return true;
  }

  // 生成工具栏
  function generateToolbar() {
    // 1. 生成搜索区域
    $('#search-container').append(searchAreaTemplate);

    // 重新填充平台下拉框
    $(".plat_choose").empty();
    $.each(platAll, function (_, item) {
      var optText = '[' + item.PlatValue + ']' + item.Name;
      if (optText == '[]') {
        optText = '';
      }
      var options = "<option value='" + item.PlatValue + "'>" + optText + "</option>";
      $(".plat_choose").append(options);
    });

    $('#floating-func-box').hide();

    // 重新渲染表单
    layui.form.render();
  }

  // 暴露公共方法
  window.PolymsgModule = {
    init: init,
    validateData: validateData,
    generateToolbar: generateToolbar
  };
})();
