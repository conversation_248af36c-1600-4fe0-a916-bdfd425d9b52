// 日志模块
(function() {

  // 初始化日志模块
  function init() {

    // 注册日志菜单配置
    registerMenuConfig();

    // 绑定日志相关事件
    bindEvents();
  }

  // 注册日志菜单配置
  function registerMenuConfig() {
    menuConfig['record'] = {
      url: '/record/get',
      cols: [[ //表头
        { field: 'plat', title: '平台', width: '10%', align: 'center' }
        , { field: 'method', title: '接口类型', width: '16%', align: 'center' }
        , { field: 'user_name', title: '会员名', width: '16%', align: 'center' }
        , { field: 'token', title: 'Token', width: '16%', align: 'center' }
        , { field: 'request_id', title: '请求ID', width: '16%', align: 'center' }
        , { field: 'req_time', title: '请求时间', width: '16%', align: 'center' }
        , { fixed: 'right', title: '详情', width: '10%', align: 'center', toolbar:
            `<script type="text/html">
              <a class="layui-btn layui-btn-xs" lay-event="record-detail">查看</a>
            </script>`
          }
      ]],
      parseData: function (res) {
        for (var i in res.data) {
          var row = res.data[i];
          var apimatch = apiAll.find(a => a.apitype == row.method);
          if (apimatch) {
            row.method = apimatch.name;
          }
          formatRowDataPlat(row);
        }
      }
    };
  }

  // 绑定日志相关事件
  function bindEvents() {
    // 监听请求日志表单提交
    layui.form.on('submit(record-search)', function (data) {
      layui.table.reload('record-table', {
        where: {
          request_id: $('#request_id').val(),
          time_start: $('#date-start').val(),
          time_end: $('#date-end').val(),
          plat: $('#record-plat').val(),
          api_type: $('#method').val(),
          user_name: $('#user_name').val(),
        }
        , page: { curr: 1 }
      });
      return false;//false：阻止表单跳转 true：表单跳转
    });

    // 请求日志工具
    layui.table.on('tool(record-tool)', function (obj) {
      var data = obj.data;
      var layEvent = obj.event;
      if (layEvent === 'record-detail') {
        window.open("/log?id=" + data.request_id);
      }
    });

    // 初始化日期选择器
    var dateTime = new Date();
    layui.laydate.render({
      elem: '#date-start'
      , type: 'datetime'
      , format: 'yyyy-MM-dd HH:mm'
      , value: new Date(new Date(dateTime.toLocaleDateString()).getTime())
    });
    layui.laydate.render({
      elem: '#date-end'
      , type: 'datetime'
      , format: 'yyyy-MM-dd HH:mm'
      , value: new Date(new Date(dateTime.toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
    });
  }

  // 日志数据校验
  function validateData(obj) {
    // 日志模块不需要特殊的数据校验
    return true;
  }

  // 日志工具栏模板
  const recordToolbarTemplate = `
  <div class="form-div" style="width: 100%;">
    <form id="record-menu-box" class="layui-form" action="">
      <div class="layui-inline" id="time_range">
        <label class="layui-form-label">时间选择</label>
        <div class="layui-input-inline">
          <input type="text" name="date-start" id="date-start" readonly=true autocomplete="off" class="layui-input">
        </div>
        <div class="layui-form-mid">-</div>
        <div class="layui-input-inline">
          <input type="text" name="date-end" id="date-end" readonly=true autocomplete="off" class="layui-input">
        </div>
      </div>

      <div class="layui-inline">
        <label class="layui-form-label">平台</label>
        <div class="layui-input-inline">
          <select class="plat_choose" id="record-plat" name="plat" lay-search="">
          </select>
        </div>
      </div>

      <div class="layui-inline">
        <label class="layui-form-label">接口类型</label>
        <div class="layui-input-inline">
          <select id="method" class="method_choose" name="method" lay-search="">
          </select>
        </div>
      </div>

      <div class="layui-inline">
        <label class="layui-form-label">会员名</label>
        <div class="layui-input-inline">
          <input type="text" id="user_name" autocomplete="off" class="layui-input">
        </div>
      </div>

      <div class="layui-inline">
        <label class="layui-form-label">请求id</label>
        <div class="layui-input-inline">
          <input type="tel" id="request_id" autocomplete="off" class="layui-input">
        </div>
      </div>

      <button lay-filter="record-search" type="submit" class="searchBtn layui-btn layui-btn-normal"
        lay-submit="">查询</button>
    </form>
  </div>
  `;

  // 生成工具栏
  function generateToolbar() {
    // 清除旧的工具栏
    $('#record-div').find('.form-div').remove();

    // 添加日志工具栏
    $('#record-div').prepend(recordToolbarTemplate);

    // 重新填充平台下拉框
    $("#record-plat").empty();
    $.each(platAll, function (_, item) {
      var optText = '[' + item.PlatValue + ']' + item.Name;
      if (optText == '[]') {
        optText = '';
      }
      var options = "<option value='" + item.PlatValue + "'>" + optText + "</option>";
      $("#record-plat").append(options);
    });

    // 重新填充接口类型下拉框
    $("#method").empty();
    $.each(apiAll, function (_, item) {
      var options = "<option value='" + item.apitype + "'>" + item.name + "</option>";
      $("#method").append(options);
    });

    // 初始化日期选择器
    var dateTime = new Date();
    layui.laydate.render({
      elem: '#date-start'
      , type: 'datetime'
      , format: 'yyyy-MM-dd HH:mm'
      , value: new Date(new Date(dateTime.toLocaleDateString()).getTime())
    });
    layui.laydate.render({
      elem: '#date-end'
      , type: 'datetime'
      , format: 'yyyy-MM-dd HH:mm'
      , value: new Date(new Date(dateTime.toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
    });

    // 重新渲染表单
    layui.form.render();
  }

  // 暴露公共方法
  window.RecordModule = {
    init: init,
    validateData: validateData,
    generateToolbar: generateToolbar
  };
})();
