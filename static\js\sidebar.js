// 侧边栏组件
console.log('sidebar.js开始加载...');
(function() {
  // 初始化侧边栏
  function initSidebar() {
    console.log('初始化侧边栏...');

    // 渲染侧边栏
    $('#sidebar-container').html(sidebarTemplate);
    console.log('侧边栏模板已渲染');

    // 绑定侧边栏按钮事件
    bindSidebarButtonEvents();
    console.log('侧边栏按钮事件已绑定');

    // 绑定导航菜单点击事件
    $("ul.layui-nav>li>a").click(function () {
      var link = $(this).data("link");
      var role = $(this).data("role");

      // 更新当前选中的菜单项
      $("ul.layui-nav>li").removeClass("layui-this");
      $(this).parent().addClass("layui-this");

      // 隐藏所有内容区域，显示当前选中的内容区域
      $('.center-body').addClass('item-hide');
      $('#' + link + '-div').removeClass('item-hide');

      // 如果是说明菜单，加载说明文档
      if (role === 'guide' && window.GuideModule && typeof window.GuideModule.loadGuideContent === 'function') {
        window.GuideModule.loadGuideContent();
      }

      // 获取当前模块对象
      var moduleKey = role.charAt(0).toUpperCase() + role.slice(1) + 'Module';
      var module = window[moduleKey];

      // 清空搜索容器
      $('#search-container').empty();

      // 清空按钮容器
      $('#module-buttons-container').empty();

      // 清空接口类型容器
      $('#method-container').empty();

      // 检查模块是否存在并实现了必要的方法
      if (module) {
        // 调用当前模块的生成工具栏函数
        if (typeof module.generateToolbar === 'function') {
          module.generateToolbar();
        } else {
          console.error('模块 ' + moduleKey + ' 未实现 generateToolbar 方法');
        }
      } else {
        console.error('找不到模块 ' + moduleKey);
      }

      // 确保menuConfig变量存在
      if (typeof menuConfig === 'undefined') {
        alert('菜单配置未加载，请刷新页面重试');
        return;
      }

      var menu_config = menuConfig[role];
      if (!menu_config) {
        console.error('找不到[' + role + ']的菜单配置');
        alert('缺少菜单配置');
        return;
      }

      var table_config = {
        elem: '#' + link + '-table'
        , height: tableHeight
        , url: menu_config.url
        , method: 'post'
        , title: '查询'
        , page: true //开启分页
        , limit: tablineHeight
        , autoSort: false
        , limits: [5, 10, 15, 20, 25, 30, 35, 40, 45, 50]
        , cols: menu_config.cols
        , parseData: menu_config.parseData
        , done: function (res, curr, count) {
          if (count > 0) {
            if (role === 'record') {
              return;
            }
            // 点击第一行
            $('#' + link + '-table').next().find('div.layui-table-body>table>tbody>tr>td>div.layui-table-cell:first').click();
          } else {
            resetDetail(role);
          }
        }

      };

      // 执行一个table实例并渲染
      layui.table.render(table_config);

    });
  }

  // 绑定侧边栏按钮事件
  function bindSidebarButtonEvents() {
    console.log('绑定侧边栏按钮事件...');
    console.log('新平台按钮元素:', $('#new-platform-btn').length);
    console.log('主题切换按钮元素:', $('#theme-switch-btn').length);

    // 新平台按钮事件
    $('#new-platform-btn').on('click', function() {
      layer.open({
        type: 2,
        title: "平台注册",
        skin: 'layui-layer-rim',
        area: ['512px', '240px'],
        closeBtn: 0,
        shadeClose: true,
        content: './static/platAdd.html',
        success: function (layero, index) {
          applyThemeToIframe(layero);
        }
      });
    });

    // 主题切换按钮事件
    $('#theme-switch-btn').on('click', function() {
      var isDark = document.getElementById('layui_theme_css').getAttribute('href');
      var themeIcon = $('#theme-switch-btn .theme-icon');
      var jsonPath = '';

      if (isDark) {
        // 切换到明亮模式
        document.getElementById('layui_theme_css').removeAttribute('href');
        themeIcon.removeClass('layui-icon-moon').addClass('layui-icon-light');
        jsonPath = "https://unpkg.com/live2d-widget-model-hijiki@1.0.5/assets/hijiki.model.json";
      } else {
        // 切换到深色模式
        document.getElementById('layui_theme_css').setAttribute('href', './static/js/layui/css/layui-theme-dark.css');
        themeIcon.removeClass('layui-icon-light').addClass('layui-icon-moon');
        jsonPath = "https://unpkg.com/live2d-widget-model-tororo@1.0.5/assets/tororo.model.json";
      }

      // 更新Live2D模型（如果存在）
      if (window.L2Dwidget && window.L2Dwidget.config) {
        window.L2Dwidget.config.model.jsonPath = jsonPath;
        window.L2Dwidget.init();
      }
    });
  }

  // 暴露公共方法
  window.SidebarComponent = {
    init: initSidebar
  };
  console.log('SidebarComponent已暴露到window对象');
})();
