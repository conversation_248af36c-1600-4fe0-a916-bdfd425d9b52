// HTML模板定义

// 侧边栏模板
const sidebarTemplate = `
<ul class="layui-nav layui-nav-tree layui-nav-side">
  <li class="layui-nav-item layui-this"><a data-link="main" data-role="order">订单</a></li>
  <li class="layui-nav-item"><a data-link="main" data-role="goods">商品</a></li>
  <li class="layui-nav-item"><a data-link="main" data-role="polymsg">消息</a></li>
  <li class="layui-nav-item"><a data-link="main" data-role="common">通用</a></li>
  <li class="layui-nav-item"><a data-link="record" data-role="record">日志</a></li>
  <li class="layui-nav-item"><a data-link="guide" data-role="guide">说明</a></li>
</ul>
<!-- 侧边栏底部按钮 -->
<div class="sidebar-bottom-buttons">
  <!-- 新平台按钮 -->
  <div class="sidebar-circle-btn" id="new-platform-btn" title="新平台">
    <i class="layui-icon layui-icon-add-1"></i>
  </div>
  <!-- 主题切换按钮 -->
  <div class="sidebar-circle-btn theme-switch-btn" id="theme-switch-btn" title="切换主题">
    <i class="layui-icon layui-icon-moon theme-icon"></i>
  </div>
</div>
`;



// 搜索区域模板
const searchAreaTemplate = `
<div class="layui-inline">
  <label class="layui-form-label">平台</label>
  <div class="layui-input-inline">
    <select class="plat_choose" id="plat_choose" name="plat_choose" lay-search=""></select>
  </div>
  <span id="method-container"></span>
</div>
<button lay-filter="main_search" id="main_search" type="submit" class="searchBtn layui-btn layui-btn-normal" lay-submit="">查询</button>
`;

// 接口类型下拉框模板
const methodSelectTemplate = `
<div class="layui-inline">
  <label class="layui-form-label">接口类型</label>
  <div class="layui-input-inline">
    <select class="method_choose" id="method_choose" name="method" lay-search=""></select>
  </div>
</div>
`;



// 主界面布局模板
const mainLayoutTemplate = `
<div style="display: flex; width: 100%;">
  <div style="width: 60%;">
    <table id="main-table" lay-filter="main_tool" style="width: 100%;"></table>
  </div>
  <!-- 右侧浮动按钮 -->
  <div class="right-floating-buttons" id="floating-func-box">
    <!-- 新增按钮 -->
    <div class="floating-circle-btn add-btn" id="floating-add-btn" title="新增">
      <i class="layui-icon layui-icon-addition"></i>
    </div>
    <!-- 保存按钮 -->
    <div class="floating-circle-btn save-btn" id="floating-save-btn" title="保存">
      <i class="layui-icon layui-icon-edit"></i>
    </div>
  </div>
  <div style="width: 40%; margin: 5px;">
    <form class="layui-form layui-form-pane" id="preview-form" action="" style="width: 100%;">
      <div class="layui-form-item">
        <label class="layui-form-label">平台</label>
        <div class="layui-input-inline">
          <input type="hidden" id="example-id" name="id">
          <input type="hidden" id="example-plat-id" name="plat_id">
          <input type="text" id="example-plat" name="plat" readonly="readonly" autocomplete="off" class="layui-input">
        </div>
      </div>
      <div class="layui-form-item push-notify">
        <label class="layui-form-label">接口</label>
        <div class="layui-input-inline push-notify-form-item">
          <input type="text" id="example-interface" name="plat" readonly="readonly" autocomplete="off"
            class="layui-input">
        </div>
      </div>
      <div class="layui-form-item push-notify">
        <label class="layui-form-label">平台</label>
        <div class="layui-input-inline push-notify-form-item">
          <select class="plat_choose" id="push_plat" lay-search="" lay-filter="push_change"></select>
        </div>
      </div>
      <div class="layui-form-item push-notify">
        <label class="layui-form-label">吉客号</label>
        <div class="layui-input-inline push-notify-form-item">
          <select id="push_user" name="user" lay-search="" lay-filter="push_change" >
            <option value="420001" selected="">420001</option>
            <option value="420002">420002</option>
            <option value="440001">440001</option>
            <option value="450001">450001</option>
            <option value="jackyun_dev">jackyun_dev</option>
          </select>
        </div>
      </div>
      <div class="layui-form-item push-notify">
        <label class="layui-form-label">店铺选择</label>
        <div class="layui-input-inline push-notify-form-item">
          <select id="push_shop" name="shop" lay-search=""></select>
        </div>
        <div class="layui-form-mid" style="padding: 0 !important;">
          <input type="checkbox" checked="" id="auth-shop-only" lay-filter="auth-shop" lay-skin="tag" title="已授权店铺" checked>
          <button type="submit" class="layui-btn" lay-submit="" lay-filter="push-notify">gate推送</button>
        </div>
      </div>
      <div class="layui-form-item">
        <label class="layui-form-label">标签</label>
        <div class="layui-input-block">
          <input type="text" id="example-tag" name="tag" autocomplete="off" class="layui-input">
        </div>
      </div>
      <div class="layui-form-item" id="feild-edit-box" style="display:none;">
        <label class="layui-form-label">字段</label>
        <div class="layui-input-inline">
          <input type="hidden" id="example-method">
          <input type="text" name="biz_feild" id="example-feild" autocomplete="off" class="layui-input" value="1">
        </div>
        <div id="mapping-info" class="layui-input-inline" style="line-height: 36px;display: none;">
          <span id="mapping-text" style="vertical-align: top;"></span>
          <span>
            <i class="layui-icon layui-icon-set" id="mapping-config-btn" style="cursor: pointer; margin-left: 5px; color: #1E9FFF;" ></i>
          </span>
        </div>
        <div id="add-mapping-info" class="layui-input-inline" style="line-height: 36px;display: none;">
          <span style="color: #666; font-size: 12px;">
            <i class="layui-icon layui-icon-add-1" id="add-mapping-config-btn" style="cursor: pointer; margin-left: 5px; color: #5FB878;" title="新增字段映射配置"></i>
          </span>
        </div>
      </div>
      <div class="layui-form-item" id="priority-edit-box" style="display: none;">
        <div class="layui-inline">
          <label class="layui-form-label">优先级</label>
          <div class="layui-input-block">
            <input type="text" name="priority" id="example-priority" lay-verify="number" autocomplete="off" class="layui-input" value="1">
          </div>
        </div>
      </div>
      <div class="layui-form-item layui-form-text" id="content-box">
        <label class="layui-form-label" style="text-indent: 25px;">详情</label>
        <div class="layui-input-block">
          <textarea name="content" id="example-content" placeholder="请输入内容" class="layui-textarea"
            style="height: 100%;"></textarea>
        </div>
      </div>
    </form>
  </div>
</div>
`;

// 日志界面布局模板
const recordLayoutTemplate = `
<div style="display: flex; width: 100%;">
  <table id="record-table" lay-filter="record-tool" style="width: 100%;"></table>
</div>
`;

// 说明界面布局模板
const guideLayoutTemplate = `
<div style="width: 100%; display: flex; justify-content: center;">
  <div id="guide-content" class="markdown-body"></div>
</div>
`;

// 表格工具栏模板已经直接添加到index.html文件中
