// 工具栏组件
(function() {
  // 初始化主界面工具栏
  function initMainToolbar() {
    // 使用CommonModule中的模板
    if (window.CommonModule && window.CommonModule.commonToolbarTemplate) {
      $('#main-div').prepend(window.CommonModule.commonToolbarTemplate);

      // 绑定新增平台按钮事件
      $('#plat_add').on('click', function () {
        layer.open({
          type: 2,
          title: "平台注册",
          skin: 'layui-layer-rim', //自定义选中色值
          area: ['512px', '240px'], //宽高
          closeBtn: 0, //不显示关闭按钮
          shadeClose: true, //开启遮罩关闭
          content: './static/platAdd.html',
          success: function (layero, index) {
            applyThemeToIframe(layero);
          }
        });
      });
    } else {
      console.error('CommonModule或commonToolbarTemplate未定义, 请确保在调用initMainToolbar之前已经初始化CommonModule');
    }
  }

  // 暴露公共方法
  window.ToolbarComponent = {
    initMainToolbar: initMainToolbar
  };
})();
