<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="Access-Control-Allow-Origin" content="*" />
  <title>请求记录-查看</title>

  <script src="./static/js/jquery.min.js"></script>
  <script src="./static/js/layui/layui.js"></script>
  <link rel="stylesheet" href="./static/js/layui/css/layui.css" media="all">
  <link rel="shortcut icon" href="./static/favicon.ico" type="image/x-icon">

  <script type="text/javascript" src="./static/js/bignumber.min.js"></script>
  <script type="text/javascript" src="./static/js/jquery.json.js"></script>
  <script type="text/javascript" src="./static/js/jsonlint.js"></script>

  <style>

    .layui-input-block {
      margin-left: 80px;
      margin-right: 80px;
    }

    .layui-elem-quote {
      margin-left: 80px;
      margin-right: 80px;
      margin-top: 10px;
      border-left: 5px solid #35c8e6 !important;
      line-height: 10px;
    }

    div.record-format {
      border-color: #eee !important;
      border-width: 1px;
      border-style: solid;
    }

  </style>

</head>

<body>

  <div>
    <div>
      <div class="layui-col-md12 layui-col-xs12">
        <blockquote class="layui-elem-quote"><i class="seraph icon-caidan"></i>请求内容</blockquote>
        <div class="layui-input-block" class="record-format">
          <textarea id="req-content" class="layui-textarea"></textarea>
        </div>
      </div>
      <div class="layui-col-md12 layui-col-xs12" style="margin-bottom: 20px;">
        <blockquote class="layui-elem-quote"><i class="seraph icon-caidan"></i>返回内容</blockquote>
        <div class="layui-input-block" class="record-format">
          <textarea id="return-content" class="layui-textarea"></textarea>
        </div>
      </div>
    </div>

    <script>

      // 数据初始化
      $(function () {

        // 取ulr参数
        var urlParam = function (name) {
          var results = new RegExp('[\?&]' + name + '=([^]*)').exec(window.location.href);
          if (results == null) {
            return null;
          }
          else {
            return results[1] || 0;
          }
        }

        // 格式化json
        var formatJson = function (json_str) {
          // 格式化
          current_json = jsonlint.parse(json_str);
          // 格式化显示
          json = JSON.stringify(current_json, undefined, 4);
          return json
        }

        $.fn.autoHeight = function () {
          function autoHeight(elem) {
            elem.style.height = 'auto';
            elem.scrollTop = 0; //防抖动
            elem.style.height = elem.scrollHeight + 10 + 'px';
          }
          this.each(function () {
            autoHeight(this);
          });
        }

        var id = urlParam('id');
        if (!id) {
          return;
        }

        // ajax 请求详情
        $.ajax({
          url: "/record/detail?reqId=" + id,
          type: 'GET',
          dataType: 'json',
          async: false,
          success: function (result) {
            if (!result.success) {
              layer.open({
                type: 1,
                area: ['512px', '384px'],
                title: '请求详情出错:' + result.msg,
                closeBtn: 0,
                shadeClose: true,
                content: ex.responseText
              });
              return;
            }

            // 格式化显示
            request = formatJson(result.request);
            return_json = formatJson(result.returnInfo);

            $("#req-content").val(request).autoHeight();
            $('#return-content').val(return_json).autoHeight();

          },
          error: function (ex) {
            layer.open({
              type: 1,
              area: ['512px', '384px'],
              title: '请求详情出错',
              closeBtn: 0,
              shadeClose: true,
              content: ex.responseText
            });
          }
        });

      });

      layui.config({
        version: '1626897823561' //为了更新 js 缓存，可忽略
      });

      //加载模块  
      layui.use(function () { //亦可加载特定模块：layui.use(['layer', 'laydate', function(){
        //得到各种内置组件
        var layer = layui.layer //弹层
          , element = layui.element //元素操作
      });

    </script>
</body>

</html>