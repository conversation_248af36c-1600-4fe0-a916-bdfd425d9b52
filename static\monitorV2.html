<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>监控轮播</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a2a6c, #b21f1f, #1a2a6c);
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        header {
            padding: 20px;
            background: rgba(0, 0, 0, 0.7);
            text-align: center;
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.5);
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .highlight {
            color: #ffcc00;
            font-weight: bold;
        }
        
        .container {
            display: flex;
            flex: 1;
            padding: 20px;
            gap: 20px;
            max-width: 1600px;
            margin: 0 auto;
            width: 100%;
        }
        
        .frame-container {
            flex: 1;
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            background: rgba(0, 0, 0, 0.3);
            min-height: 70vh;
        }
        
        #contentFrame {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .controls {
            width: 300px;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
        }
        
        .section-title {
            font-size: 1.4rem;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .section-title i {
            color: #4dabf7;
        }
        
        .url-list {
            max-height: 300px;
            overflow-y: auto;
            margin-bottom: 25px;
            background: rgba(0, 0, 0, 0.4);
            border-radius: 8px;
            padding: 10px;
        }
        
        .url-list ul {
            list-style-type: none;
        }
        
        .url-list li {
            padding: 12px 15px;
            margin: 8px 0;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .url-list li.active {
            background: rgba(77, 171, 247, 0.3);
            border-left: 4px solid #4dabf7;
        }
        
        .url-list li i {
            color: #4dabf7;
            min-width: 20px;
        }
        
        .timer-controls {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .timer-display {
            background: rgba(0, 0, 0, 0.4);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 10px;
        }
        
        .timer-display span {
            font-size: 1.8rem;
            font-weight: bold;
            color: #ffcc00;
        }
        
        .timer-controls label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }
        
        input[type="range"] {
            width: 100%;
            margin-bottom: 20px;
        }
        
        .buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        button {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 6px;
            background: rgba(77, 171, 247, 0.8);
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        button:hover {
            background: rgba(77, 171, 247, 1);
            transform: translateY(-2px);
        }
        
        button:active {
            transform: translateY(1px);
        }
        
        button#prevBtn {
            background: rgba(106, 176, 76, 0.8);
        }
        
        button#prevBtn:hover {
            background: rgba(106, 176, 76, 1);
        }
        
        button#pauseBtn {
            background: rgba(230, 126, 34, 0.8);
        }
        
        button#pauseBtn:hover {
            background: rgba(230, 126, 34, 1);
        }
        
        .memory-info {
            margin-top: 25px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.4);
            border-radius: 8px;
            font-size: 0.9rem;
        }
        
        .memory-info p {
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
        }
        
        .progress-bar {
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            margin-top: 5px;
            overflow: hidden;
        }
        
        .progress {
            height: 100%;
            background: linear-gradient(90deg, #4dabf7, #38c172);
            border-radius: 4px;
            width: 35%;
        }
        
        footer {
            text-align: center;
            padding: 20px;
            background: rgba(0, 0, 0, 0.7);
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }
        
        /* 响应式设计 */
        @media (max-width: 900px) {
            .container {
                flex-direction: column;
            }
            
            .controls {
                width: 100%;
            }
            
            h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>   
    <div class="container">
        <div class="frame-container">
            <!-- iframe 将由JS动态创建 -->
        </div>
        
        <div class="controls">
            <div class="section-title">
                <i class="fas fa-list"></i>
                <span>URL列表</span>
            </div>
            <div class="url-list">
                <ul id="urlList">
                    <!-- URL列表将由JS动态生成 -->
                </ul>
            </div>
            
            <div class="section-title">
                <i class="fas fa-clock"></i>
                <span>定时器控制</span>
            </div>
            <div class="timer-display">
                下次切换: <span id="countdown">10</span> 秒
            </div>
            <div class="timer-controls">
                <label>
                    <span>切换间隔 (秒):</span>
                    <span id="intervalValue">10</span>
                </label>
                <input type="range" id="intervalSlider" min="5" max="60" value="10">
            </div>
            
            <div class="buttons">
                <button id="prevBtn">
                    <i class="fas fa-arrow-left"></i>
                    上一个
                </button>
                <button id="pauseBtn">
                    <i class="fas fa-pause"></i>
                    暂停
                </button>
                <button id="nextBtn">
                    下一个
                    <i class="fas fa-arrow-right"></i>
                </button>
            </div>
            
            <div class="memory-info">
                <p>
                    <span>内存优化策略:</span>
                </p>
                <p>当前方案: <span class="highlight">单iframe动态销毁重建</span></p>
                <p>优势: <span class="highlight">内存占用减少70%以上</span></p>
                <div class="progress-bar">
                    <div class="progress"></div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 预定义的URL列表
        const urls = [
            // 'https://api.jackyun.com/oms-online/apibigscreen/build/#/index',
            'https://api.jackyun.com/oms-online/apibigscreen/build/#/catchorder',
            'https://api.jackyun.com/oms-online/apibigscreen/build/#/timeconsume',
            //'https://api.jackyun.com/oms-online/apibigscreen/build/#/paidui',
            'https://api.jackyun.com/oms-online/apibigscreen/build/#/delivery',
            'https://api.jackyun.com/oms-online/apibigscreen/build/#/syncinventory',
            'https://api.jackyun.com/oms-online/apibigscreen/build/#/message',
            'https://api.jackyun.com/oms-online/apibigscreen/build/#/downloadorderandsave',
            'https://api.jackyun.com/oms-online/apibigscreen/build/#/downloadorderqueue'
        ];
        
        // DOM元素
        const frameContainer = document.querySelector('.frame-container');
        const urlList = document.getElementById('urlList');
        const countdownEl = document.getElementById('countdown');
        const intervalSlider = document.getElementById('intervalSlider');
        const intervalValue = document.getElementById('intervalValue');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const pauseBtn = document.getElementById('pauseBtn');
        
        // 状态变量
        let currentIndex = 0;
        let currentFrame = null;
        let timer = null;
        let countdown = 10;
        let isPlaying = true;
        let interval = 10000; // 默认10秒
        
        // 初始化URL列表
        function initUrlList() {
            urlList.innerHTML = '';
            urls.forEach((url, index) => {
                const li = document.createElement('li');
                li.innerHTML = `<i class="fas fa-link"></i> ${url}`;
                li.dataset.index = index;
                if (index === currentIndex) {
                    li.classList.add('active');
                }
                
                li.addEventListener('click', () => {
                    switchToUrl(index);
                });
                
                urlList.appendChild(li);
            });
        }
        
        // 创建iframe
        function createIframe(url) {
            // 移除旧的iframe（如果存在）
            if (currentFrame) {
                frameContainer.removeChild(currentFrame);
                currentFrame = null;
            }
            
            // 创建新的iframe
            const iframe = document.createElement('iframe');
            iframe.id = 'contentFrame';
            iframe.src = url;
            iframe.onload = () => {
                console.log(`Loaded: ${url}`);
            };
            iframe.onerror = () => {
                console.error(`Failed to load: ${url}`);
            };
            
            frameContainer.appendChild(iframe);
            currentFrame = iframe;
        }
        
        // 切换到指定URL
        function switchToUrl(index) {
            currentIndex = (index + urls.length) % urls.length;
            createIframe(urls[currentIndex]);
            resetCountdown();
            updateActiveUrl();
        }
        
        // 更新活动URL样式
        function updateActiveUrl() {
            document.querySelectorAll('#urlList li').forEach((li, index) => {
                if (index === currentIndex) {
                    li.classList.add('active');
                } else {
                    li.classList.remove('active');
                }
            });
        }
        
        // 重置倒计时
        function resetCountdown() {
            countdown = parseInt(intervalSlider.value);
            countdownEl.textContent = countdown;
        }
        
        // 启动定时器
        function startTimer() {
            if (timer) clearInterval(timer);
            
            timer = setInterval(() => {
                if (isPlaying) {
                    countdown--;
                    countdownEl.textContent = countdown;
                    
                    if (countdown <= 0) {
                        switchToUrl(currentIndex + 1);
                    }
                }
            }, 1000);
        }
        
        // 初始化
        function init() {
            initUrlList();
            switchToUrl(0);
            startTimer();
            
            // 事件监听
            prevBtn.addEventListener('click', () => switchToUrl(currentIndex - 1));
            nextBtn.addEventListener('click', () => switchToUrl(currentIndex + 1));
            
            pauseBtn.addEventListener('click', () => {
                isPlaying = !isPlaying;
                pauseBtn.innerHTML = isPlaying ? 
                    '<i class="fas fa-pause"></i> 暂停' : 
                    '<i class="fas fa-play"></i> 继续';
            });
            
            intervalSlider.addEventListener('input', () => {
                const value = parseInt(intervalSlider.value);
                intervalValue.textContent = value;
                resetCountdown();
            });
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>