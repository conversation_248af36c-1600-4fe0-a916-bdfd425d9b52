<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="Access-Control-Allow-Origin" content="*" />
  <title>管家订单推送</title>

  <script src="./js/jquery.min.js"></script>
  <script src="./js/layui/layui.js"></script>

  <link rel="stylesheet" href="./js/layui/css/layui.css" media="all">
  <link id="layui_theme_css" rel="stylesheet" href="./js/layui/css/layui-theme-dark.css">

  <style>

    div.layui-input-block .layui-input {
      width: 380px;
    }

    div.layui-select-title .layui-input {
      width: 200px;
    }

    .label-inline {
      width: auto !important;
    }

    .layui-form-mid {
      padding: 0 !important;
    }

    .form_plat {
      margin-top: 20px;
      margin-bottom: 20px;
    }

    .form-submit {
      float: right;
      margin-right: 65px;
    }
  </style>

</head>

<body>
  <div style="width: 100%; height: 100%;position: absolute;">
    <form class="layui-form" action="">
      <input type="hidden" id="plat" />
      <input type="hidden" id="order_Id" />
      <div class="layui-form-item form_plat">
          <label class="layui-form-label">推送地址</label>
          <div class="layui-input-block">
            <input type="text" id="pushHost" lay-verify="required" autocomplete="off" class="layui-input" value="localhost">
            <input type="number" id="pushPort" lay-verify="required" autocomplete="off" class="layui-input" value="7864">
            <input type="text" id="pushUrl" lay-verify="required" readonly autocomplete="off" class="layui-input" value="/router/MessageNotification" >
          </div>
      </div>
      <div class="layui-form-item form_plat">
          <label class="layui-form-label">appKey</label>
          <div class="layui-input-block">
            <input type="text" lay-verify="required" id="appKey" autocomplete="off" class="layui-input" value="f2c0a820992743f8b293f4b027265df0">
          </div>
      </div>
      <div class="layui-form-item form_plat">
        <label class="layui-form-label">会员名</label>
        <div class="layui-input-block">
          <input type="text" lay-verify="required" id="user" autocomplete="off" class="layui-input" value="api2017">
        </div>
      </div>
      <div class="layui-form-item form_plat">
          <label class="layui-form-label">店铺token</label>
          <div class="layui-input-block">
            <input type="text" lay-verify="required" id="shopToken" autocomplete="off" class="layui-input">
          </div>
      </div>
      <div class="layui-form-item form_plat">
          <label class="layui-form-label">指定单号</label>
          <div class="layui-input-block">
            <input type="text" id="order_no" autocomplete="off" class="layui-input">
          </div>
      </div>
      <div class="layui-form-item form_plat">
          <label class="layui-form-label">指定状态</label>
          <div class="layui-input-block">
            <input type="text" id="order_status" autocomplete="off" class="layui-input">
          </div>
      </div>
      <div class="layui-form-item">
        <div class="layui-inline">
          <label class="layui-form-label">查询详情</label>
          <div class="layui-input-block">
          <input type="checkbox" id="fill_up" lay-skin="switch" lay-text="ON|OFF">
          </div>
        </div>
        <div class="layui-inline">
        <label class="layui-form-label">时间修正</label>
        <div class="layui-input-block">
         <input type="checkbox" checked="" id="fix_time" lay-skin="switch" lay-text="ON|OFF">
        </div>
        </div>
       </div>
      <div class="layui-form-item" style="position: fixed;bottom: 40px;right: 15px;">
        <div class="layui-input-block form-submit">
          <button type="submit" class="layui-btn layui-bg-blue" lay-submit="" data-id="order" lay-filter="post">openapi推送</button>
        </div>
      </div>
    </form>

  </div>

  <script>

    layui.config({
      version: '1626897823562' //为了更新 js 缓存，可忽略
    });

    // 加载模块  
    layui.use(function () {

      var layer = layui.layer //弹层
        , element = layui.element //元素操作
        , dropdown = layui.dropdown //下拉菜单
        , form = layui.form

      // 监听提交
      form.on('submit(post)', function (data) {

        var pushHost = $("#pushHost").val();
        if (!pushHost) {
          layer.msg('请输入推送服务ip', {icon: 2});
          return false;
        }
        var pushPort = $("#pushPort").val();
        if (!pushPort || pushPort <= 0 || pushPort > 65535) {
          layer.msg('请输入正确的推送服务端口号', {icon: 2});
          return false;
        }
        var pushUrl = $("#pushUrl").val();
        if (!pushUrl) {
          layer.msg('请输入推送服务地址', {icon: 2});
          return false;
        }

        var appKey = $("#appKey").val();
        var shopToken = $("#shopToken").val();
        var plat = $('#plat').val();
        var user = $('#user').val();
        var orderId = $('#order_Id').val();
        var orderNo = $('#order_no').val();
        var orderStatus = $('#order_status').val();
        var fixTime = $('#fix_time').prop('checked');
        var fillUp = $('#fill_up').prop('checked');

        var index = window.parent.layer.load(0, {shade: false});

        $.ajax({
              url: "/order/openapipush",
              type: 'POST',
              async: false,
              data: {
                pushhost: pushHost,
                pushport: pushPort,
                pushurl: pushUrl,
                appkey: appKey, 
                platid: plat, 
                user: user, 
                token: shopToken, 
                orderid: orderId,
                orderno: orderNo, 
                orderstatus: orderStatus, 
                fillup: fillUp,
                fixtime: fixTime,
                esapi: 'true'
                },
              dataType: "text",
              success: function (data) {
                window.parent.layer.close(index); // 关闭 loading
                var rep = window.parent.formatJson(data);
                parent.layer.open({
                  type: 1,
                  title: "推送接口返回",
                  area: ['524px', '600px'],
                  shadeClose: true,
                  content: '<textarea style="width: 500px;height: 528px; margin: 8px;padding-left: 5px;border: 1px solid #F0F0F0;">' + rep + '</textarea>'
                });
              },
              error: function (ex) {
                window.parent.layer.close(index); // 关闭 loading
                window.parent.layer.msg('提交失败：' + str(ex), { icon: 2 });
              }
            });

        return false;
      });

    });

  </script>
</body>

</html>