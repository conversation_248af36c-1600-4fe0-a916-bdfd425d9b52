<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="Access-Control-Allow-Origin" content="*" />
  <title>订单样例库-新增</title>

  <script src="./js/jquery.min.js"></script>
  <script src="./js/layui/layui.js"></script>

  <link rel="stylesheet" href="./js/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="./js/layui/css/layui-theme-dark.css" id="layui_theme_css" >

  <style>
    .layui-textarea{
      height: 450px;
    }
    .form-submit{
      float: right;
      margin-right: 65px;
    }
  </style>

</head>

<body>
  <div>
    <form class="layui-form" action="" style="margin-right: 40px; margin-top: 20px;">
      <div class="layui-form-item">
        <label class="layui-form-label">平台</label>
        <div class="layui-input-block">
          <select id="plat" name="plat" lay-search="">
          </select>
        </div>
      </div>
      <div class="layui-form-item">
        <label class="layui-form-label">订单数据</label>
        <div class="layui-input-block">
          <textarea name="content" placeholder="请输入内容" class="layui-textarea"></textarea>
        </div>
      </div>
      <div class="layui-form-item">
        <label class="layui-form-label">标签</label>
        <div class="layui-input-block">
          <input type="text" name="tag" autocomplete="off" class="layui-input">
        </div>
      </div>
      <div class="layui-form-item">
        <div class="layui-inline">
          <label class="layui-form-label">优先级</label>
          <div class="layui-input-block">
            <input type="text" name="priority" lay-verify="required|number" autocomplete="off" class="layui-input" value="1">
          </div>
        </div>
      </div>
      <div class="layui-form-item">
        <div class="layui-input-block form-submit">
          <button type="submit" class="layui-btn" lay-submit="" lay-filter="post">立即提交</button>
          <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </div>
      </div>
    </form>

  </div>

  <div style="min-height: 100%; margin-bottom: -100px;">
    <table id="demo" lay-filter="test" style="width: 100%;"></table>
  </div>

<script>
    // 平台列表
    var platAll = [{ "PlatValue": "", "Name": "" }];
    $(function () {
      // 加载平台列表    
      $.ajax({
        url: "/plat/getall",
        type: 'GET',
        dataType: 'json',
        async: false,
        success: function (data) {
          platAll = platAll.concat(data);
          // 填充下拉列表
          $.each(platAll, function (i, item) {
            var optText = '[' + item.PlatValue + ']' + item.Name;
            if (optText == '[]') {
              optText = ''
            }
            var options = "<option value='" + item.PlatValue + "'>" + optText + "</option>";
            $("#plat").append(options);
          });
          // 重新渲染表单
          layui.form.render();
        },
        error: function (ex) {
          console.log(ex)
        }
      });
    })

    layui.config({
      version: '1626897823561' //为了更新 js 缓存，可忽略
    });

    //加载模块  
    layui.use(function () { //亦可加载特定模块：layui.use(['layer', 'laydate', function(){
      //得到各种内置组件
      var layer = layui.layer //弹层
        , element = layui.element //元素操作
        , dropdown = layui.dropdown //下拉菜单
        , form = layui.form

      // 监听提交
      form.on('submit(post)', function (data) {
        if (data.field.plat == '') {
          layer.alert('请选择平台', { icon: 2, title: '错误' })
          return false;
        }
        if (data.field.content == '') {
          layer.alert('请不要录入空数据', { icon: 2, title: '错误' })
          return false;
        }
        var obj = null;
        // 订单数据格式校验
        try {
          obj = window.parent.jsonlint.parse(data.field.content.toLowerCase());
          if (typeof obj == 'object' && obj) {
            if (Object.prototype.toString.call(obj) === '[object Array]') {
              layer.alert('多个订单须分多次录入，不要录入订单列表', { icon: 2, title: '错误' })
              return false;
            }
            if (!obj.platorderno || !obj.goodinfos) {
              layer.alert('缺少平台订单号或商品信息，请检查后重新录入', { icon: 2, title: '错误' })
              return false;
            }
          } else {
            layer.alert('数据格式非object，请检查后重新录入', { icon: 2, title: '错误' })
            return false;
          }
        } catch (e) {
          layer.alert('数据格式不正确，请检查后重新录入', { icon: 2, title: '错误' })
          return false;
        }
        // 压缩
        var t = data.field.content.replace(/[\r\n]/g, "")
        for (var n = [], o = !1, i = 0, r = t.length; r > i; i++) {
          var a = t.charAt(i);
          o && a === o ? "\\" !== t.charAt(i - 1) && (o = !1) : o || '"' !== a && "'" !== a ? o || " " !== a && "	" !== a || (a = "") : o = a, n.push(a)
        }
        t = n.join("");

        // 提交   
        $.ajax({
          url: "/order/add",
          type: 'POST',
          async: false,
          data: { plat : data.field.plat, content : t, tag : data.field.tag, priority : data.field.priority ?? 1 },
          dataType: "text",
          success: function (data) {
            if (data == 'success') {
              var mylay = parent.layer.getFrameIndex(window.name);
              parent.layer.msg('已提交', { icon: 1 });
              parent.$('#main-div button.searchBtn').trigger("click");  // 触发刷新
              parent.layer.close(mylay);
              return;
            }
            layer.msg('提交失败：' + data, { icon: 0 });
          },
          error: function (ex) {
            layer.msg('提交失败：' + str(ex), { icon: 2 });
          }
        });
        return false;
      });

    });

  </script>
</body>

</html>