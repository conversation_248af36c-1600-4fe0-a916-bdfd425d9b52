<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="Access-Control-Allow-Origin" content="*" />
  <title>订单推送</title>

  <script src="./js/jquery.min.js"></script>
  <script src="./js/layui/layui.js"></script>

  <link rel="stylesheet" href="./js/layui/css/layui.css" media="all">
  <link id="layui_theme_css" rel="stylesheet" href="./js/layui/css/layui-theme-dark.css">

  <style>

    div.layui-input-block .layui-input {
      width: 380px;
    }

    div.layui-select-title .layui-input {
      width: 200px;
    }

    .label-inline {
      width: auto !important;
    }

    .layui-form-mid {
      padding: 0 !important;
    }

    .form_plat {
      margin-top: 20px;
      margin-bottom: 20px;
    }

    .form-submit {
      float: right;
      margin-right: 65px;
    }
  </style>

</head>

<body>
  <div style="width: 100%; height: 100%;position: absolute;">
    <form class="layui-form" action="" style="width: 100%;height: 100%;">
      <input type="hidden" id="plat" />
      <input type="hidden" id="order_Id" />
      <div class="layui-inline form_plat">
        <label class="layui-form-label">吉客号</label>
        <div class="layui-input-inline">
          <select id="user" name="user" lay-search="" lay-filter="user">
            <option value="420001" selected="">420001</option>
            <option value="420002">420002</option>
            <option value="440001">440001</option>
            <option value="450001">450001</option>
            <option value="jackyun_dev">jackyun_dev</option>
          </select>
        </div>
      </div>
      <div class="layui-form-item">
        <div class="layui-inline">
          <label class="layui-form-label">店铺选择</label>
          <div class="layui-input-inline">
            <select id="shop" name="shop" lay-search=""></select>
          </div>
          <label class="layui-form-label label-inline">已授权店铺</label>
          <div class="layui-form-mid"> 
            <input type="checkbox" checked="" name="tag" id="auth-shop-only" lay-skin="switch" lay-filter="auth-shop" lay-text="ON|OFF">
          </div>
        </div>
      </div>
      <div class="layui-form-item layui-form-text">
        <label class="layui-form-label">指定数量</label>
        <div class="layui-input-block">
          <input type="number" name="tag" id="order-count" autocomplete="off" class="layui-input" value="1">
        </div>
      </div>
      <div class="layui-form-item">
        <div class="layui-inline">
          <label class="layui-form-label">指定单号</label>
          <div class="layui-input-block">
            <input type="text" name="tag" id="order-no" autocomplete="off" class="layui-input">
          </div>
        </div>
      </div>
      <div class="layui-form-item">
        <div class="layui-inline">
          <label class="layui-form-label">指定状态</label>
          <div class="layui-input-block">
            <input type="text" name="tag" id="order-status" autocomplete="off" class="layui-input">
          </div>
        </div>
      </div>
      <div class="layui-form-item"> <label class="layui-form-label">时间修正</label>
        <div class="layui-input-block">
         <input type="checkbox" checked="" name="tag" id="fix-time" lay-skin="switch" lay-text="ON|OFF">
        </div>
       </div>
      <div class="layui-form-item" style="position: fixed;bottom: 40px;right: 15px;">
        <div class="layui-input-block form-submit">
          <button type="submit" class="layui-btn" lay-submit="" data-id="order" lay-filter="post">order推送</button>
        </div>
      </div>
    </form>

  </div>

  <div style="min-height: 100%; margin-bottom: -100px;">
    <table id="demo" lay-filter="test" style="width: 100%;"></table>
  </div>

  <script>

    var loadShop = function () {
      var plat = $('#plat').val();
      var user = $('#user').val();
      var auth_shop_only = $('#auth-shop-only').prop('checked');
      $("#shop").empty();
      $.ajax({
        url: "/shop/getShopByPlat",
        type: 'POST',
        async: false,
        data: { user: user, plat: plat, auth_shop_only: auth_shop_only },
        dataType: "text",
        success: function (ret) {
          var data = JSON.parse(ret)
          if (data.length > 0) {
            $.each(data, function (i, item) {
              var options = "<option value='" + item.shop_id + "'>" + item.shop_name + "</option>";
              $("#shop").append(options);
            });
            // 重新渲染表单
            layui.form.render();
          }
        },
        error: function (ex) {
          layer.msg('店铺加载失败：' + ex);
        }
      });
    }

    layui.config({
      version: '1626897823561' //为了更新 js 缓存，可忽略
    });

    //加载模块  
    layui.use(function () { //亦可加载特定模块：layui.use(['layer', 'laydate', function(){
      //得到各种内置组件
      var layer = layui.layer //弹层
        , element = layui.element //元素操作
        , dropdown = layui.dropdown //下拉菜单
        , form = layui.form

      // 监听提交
      form.on('submit(post)', function (data) {
        var method = $(data.elem).data('id');
        var shopId = $("#shop").val();
        var plat = $('#plat').val();
        var user = $('#user').val();
        var orderId = $('#order_Id').val();
        var orderNo = $('#order-no').val();
        var orderStatus = $('#order-status').val();
        var orderCount = $('#order-count').val();
        var fix_time = $('#fix-time').prop('checked');
        var url = "/order/push";
        if (orderCount && orderCount > 1){
          url = "/order/pushMany"
        }

        var index = window.parent.layer.load(0, {shade: false});

        $.ajax({
              url: url,
              type: 'POST',
              async: false,
              data: { push: method, shopid: shopId, platid: plat, user: user, orderid: orderId,
                 orderno: orderNo, orderstatus: orderStatus, count: orderCount, fixTime:fix_time },
              dataType: "text",
              success: function (data) {
                window.parent.layer.close(index); // 关闭 loading
                var rep = window.parent.formatJson(data);
                parent.layer.open({
                  type: 1,
                  title: "推送接口返回",
                  area: ['524px', '600px'],
                  shadeClose: true,
                  content: '<textarea style="width: 500px;height: 528px; margin: 8px;padding-left: 5px;border: 1px solid #F0F0F0;">' + rep + '</textarea>'
                });
              },
              error: function (ex) {
                window.parent.layer.close(index); // 关闭 loading
                window.parent.layer.msg('提交失败：' + str(ex), { icon: 2 });
              }
            });

        return false;
      });
      // 监听下拉框
      form.on('select(user)', function (data) {
        loadShop();
      });
      form.on('switch(auth-shop)', function (data) {
        loadShop();
      });
    });

  </script>
</body>

</html>