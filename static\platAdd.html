<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="Access-Control-Allow-Origin" content="*" />
  <title>平台注册</title>

  <script src="./js/jquery.min.js"></script>
  <script src="./js/layui/layui.js"></script>
  
  <link rel="stylesheet" href="./js/layui/css/layui.css" media="all">
  <link id="layui_theme_css" rel="stylesheet" href="./js/layui/css/layui-theme-dark.css">

  <style>
    .form-submit{
      float: right;
    }
  </style>

</head>

<body>
  <div>
    <form class="layui-form" action="" style="margin-right: 40px; margin-top: 20px;">
      <div class="layui-form-item">
        <label class="layui-form-label">平台值</label>
        <div class="layui-input-block">
          <input type="number" name="platId" lay-verify="required" lay-reqtext="平台值岂能为空？" placeholder="请输入" autocomplete="off" class="layui-input">
        </div>
      </div>
      <div class="layui-form-item">
        <label class="layui-form-label">平台名称</label>
        <div class="layui-input-block">
          <input type="text" name="name" lay-verify="required" lay-reqtext="名称岂能为空？" placeholder="请输入" autocomplete="off" class="layui-input">
        </div>
      </div>
      <div class="layui-form-item">
        <div class="layui-input-block form-submit">
          <button type="submit" class="layui-btn" lay-submit="" lay-filter="post">立即提交</button>
        </div>
      </div>
    </form>

  </div>

  <div style="min-height: 100%; margin-bottom: -100px;">
    <table id="demo" lay-filter="test" style="width: 100%;"></table>
  </div>

<script>
    layui.config({
      version: '1626897823561' //为了更新 js 缓存，可忽略
    });

    //加载模块  
    layui.use(function () { //亦可加载特定模块：layui.use(['layer', 'laydate', function(){
      //得到各种内置组件
      var layer = layui.layer //弹层
        , element = layui.element //元素操作
        , dropdown = layui.dropdown //下拉菜单
        , form = layui.form

      // 监听提交
      form.on('submit(post)', function (data) {
        if (data.field.content == '') {
          layer.alert('请不要录入空数据', { icon: 2, title: '错误' })
          return false;
        }

        // 提交   
        $.ajax({
          url: "/plat/add",
          type: 'POST',
          async: false,
          data: { platVal: data.field.platId, platName : data.field.name },
          dataType: "text",
          success: function (data) {
            if (data == 'success') {
              var mylay = parent.layer.getFrameIndex(window.name);
              parent.layer.msg('已提交, 请刷新页面后查看平台列表', { icon: 1 });
              parent.layer.close(mylay);
              return;
            }
            layer.msg('提交失败：' + data, { icon: 0 });
          },
          error: function (ex) {
            layer.msg('提交失败：' + str(ex), { icon: 2 });
          }
        });
        return false;
      });

    });

  </script>
</body>

</html>