#!/bin/bash

# 停止Gunicorn进程
echo "正在停止FastAPI应用..."

# 方法1: 通过PID文件停止
if [ -f "./logs/gunicorn.pid" ]; then
    PID=$(cat ./logs/gunicorn.pid)
    if kill -0 $PID 2>/dev/null; then
        echo "通过PID文件停止进程: $PID"
        kill -TERM $PID
        sleep 2
        if kill -0 $PID 2>/dev/null; then
            echo "强制停止进程: $PID"
            kill -KILL $PID
        fi
        rm -f ./logs/gunicorn.pid
    else
        echo "PID文件中的进程不存在，删除PID文件"
        rm -f ./logs/gunicorn.pid
    fi
else
    echo "PID文件不存在，尝试通过进程名停止"
fi

# 方法2: 通过进程名停止 (备用方法)
pkill -f "gunicorn main:app"

echo "FastAPI应用已停止"
