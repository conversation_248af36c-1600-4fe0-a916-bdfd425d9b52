#!/bin/bash

# 停止 uWSGI 服务的简化脚本

# 1. 尝试通过 PID 文件停止
if [ -f "./logs/uwsgi-flask.pid" ]; then
    PID=$(cat "./logs/uwsgi-flask.pid")
    
    echo "🛑 尝试优雅停止 uWSGI (PID: $PID)..."
    kill -15 "$PID" 2>/dev/null
    
    # 等待最多 3 秒
    sleep 3
    
    # 检查进程是否仍在运行
    if ps -p "$PID" >/dev/null; then
        echo "❌ 优雅停止失败，准备强制终止..."
    else
        echo "✅ uWSGI 已停止"
        rm -f "./logs/uwsgi-flask.pid"
        exit 0
    fi
fi

# 2. 如果 PID 文件不存在或优雅停止失败，使用 pkill 强制终止
echo "🛑 强制终止所有 uwsgi 进程..."
pkill -f "uwsgi uwsgi.ini"

# 检查是否还有残留进程
if pgrep -f "uwsgi uwsgi.ini" >/dev/null; then
    echo "❌ 仍有残留进程，尝试更强力终止..."
    pkill -9 -f "uwsgi uwsgi.ini"
fi

# 最终检查
if ! pgrep -f "uwsgi uwsgi.ini" >/dev/null; then
    echo "✅ 所有 uWSGI 进程已终止"
    # 清理可能存在的 PID 文件
    rm -f "./logs/uwsgi-flask.pid"
else
    echo "❌ 警告：仍有 uWSGI 进程在运行！请手动检查"
    exit 1
fi