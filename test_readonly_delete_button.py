#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 readonly 模式下删除按钮隐藏功能
"""

import sys
import os
import json

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.service import commonExampleService

def test_readonly_delete_button():
    """测试 readonly 模式下删除按钮隐藏功能"""
    
    print("开始测试 readonly 模式下删除按钮隐藏功能...")
    
    try:
        # 创建服务实例
        common_service = commonExampleService()
        
        print("✓ 服务实例创建成功")
        
        # 1. 测试基本功能
        print("\n1. 测试基本功能:")
        
        # 测试 getById 方法
        get_by_id_postvars = {'id': '1'}
        result_json = common_service.getById(get_by_id_postvars)
        result_obj = json.loads(result_json)
        
        if result_obj.get('code') == 0 and result_obj.get('data'):
            data = result_obj['data'][0] if isinstance(result_obj['data'], list) else result_obj['data']
            print(f"  ✓ getById 功能正常")
            print(f"    mappingId: {data.get('mappingId', 'N/A')}")
            print(f"    mappingFeild: {data.get('mappingFeild', 'N/A')}")
            print(f"    biz_feild: {data.get('biz_feild', 'N/A')}")
            
            mapping_id = data.get('mappingId')
            mapping_feild = data.get('mappingFeild')
            biz_feild = data.get('biz_feild')
            
            # 2. 测试 readonly 逻辑
            print("\n2. 测试 readonly 逻辑:")
            
            # 模拟前端 readonly 判断逻辑
            readonly_condition = mapping_id and mapping_feild
            readonly_value = 'readonly' if readonly_condition else ''
            is_readonly = readonly_value == 'readonly'
            
            print(f"  mappingId 存在: {bool(mapping_id)}")
            print(f"  mappingFeild 存在: {bool(mapping_feild)}")
            print(f"  readonly 条件: {readonly_condition}")
            print(f"  readonly 值: '{readonly_value}'")
            print(f"  是否只读模式: {is_readonly}")
            
            # 3. 测试按钮显示逻辑
            print("\n3. 测试按钮显示逻辑:")
            
            if is_readonly:
                print("  ✓ 只读模式 - 删除按钮应该隐藏")
                print("    HTML 中不会包含 id='mapping-del-btn' 的按钮")
                print("    用户无法删除现有的映射配置")
            else:
                print("  ✓ 编辑模式 - 删除按钮应该显示")
                print("    HTML 中会包含 id='mapping-del-btn' 的按钮")
                print("    用户可以删除映射配置")
            
            # 4. 测试不同场景
            print("\n4. 测试不同场景:")
            
            scenarios = [
                {
                    'name': '有映射配置的记录',
                    'mappingId': '123',
                    'mappingFeild': 'testField',
                    'expected_readonly': True
                },
                {
                    'name': '没有映射配置的记录',
                    'mappingId': '',
                    'mappingFeild': '',
                    'expected_readonly': False
                },
                {
                    'name': '只有mappingId没有mappingFeild',
                    'mappingId': '123',
                    'mappingFeild': '',
                    'expected_readonly': False
                },
                {
                    'name': '只有mappingFeild没有mappingId',
                    'mappingId': '',
                    'mappingFeild': 'testField',
                    'expected_readonly': False
                }
            ]
            
            for scenario in scenarios:
                readonly_condition = scenario['mappingId'] and scenario['mappingFeild']
                is_readonly = readonly_condition
                
                status = "✓" if is_readonly == scenario['expected_readonly'] else "✗"
                button_status = "隐藏" if is_readonly else "显示"
                
                print(f"    {status} {scenario['name']}: 删除按钮{button_status}")
                print(f"      mappingId: '{scenario['mappingId']}', mappingFeild: '{scenario['mappingFeild']}'")
                print(f"      readonly: {is_readonly}, 期望: {scenario['expected_readonly']}")
            
            # 5. 测试字段映射查询
            if mapping_id:
                print("\n5. 测试字段映射查询:")
                mapping_postvars = {'mappingid': mapping_id}
                mapping_result_json = common_service.getFieldMapping(mapping_postvars)
                mapping_result_obj = json.loads(mapping_result_json)
                
                if mapping_result_obj.get('code') == 0:
                    feild_map = mapping_result_obj.get('data', {}).get('feild_map', '')
                    print(f"  ✓ 字段映射查询正常")
                    print(f"    映射数据: {feild_map}")
                else:
                    print(f"  ✗ 字段映射查询失败: {mapping_result_obj.get('msg')}")
            else:
                print("\n5. 跳过字段映射查询（没有 mappingId）")
                
        else:
            print(f"  ✗ getById 功能失败: {result_obj.get('msg')}")
        
        print("\n✓ readonly 模式下删除按钮隐藏功能测试完成")
        print("\n📝 功能说明:")
        print("  1. 当 mappingId 和 mappingFeild 都存在时，进入只读模式")
        print("  2. 只读模式下，删除按钮不会在 HTML 中生成")
        print("  3. 非只读模式下，删除按钮正常显示并可以使用")
        print("  4. 事件绑定会检查按钮是否存在，避免绑定到不存在的元素")
        print("\n🔧 技术实现:")
        print("  - 使用条件表达式控制按钮 HTML 的生成")
        print("  - 使用 $('#mapping-del-btn').length > 0 检查按钮存在性")
        print("  - readonly 变量控制输入框的只读状态")
        print("  - isReadonly 变量控制删除按钮的显示/隐藏")
        
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_readonly_delete_button()
