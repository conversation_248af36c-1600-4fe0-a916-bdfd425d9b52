import json
import datetime
import decimal
import traceback
import logging

from core.base import *


# 序列化方法
def decode(obj):
    if isinstance(obj, bytes):
        return str(obj, encoding='utf-8')
    if isinstance(obj, (datetime.datetime,)):
        return obj.isoformat()
    if isinstance(obj, (decimal.Decimal,)):
        return str(obj)
    if hasattr(obj, '__dict__'):
        return obj.__dict__
    return None


# 过滤空字符串属性的序列化方法
def decode_filter_empty_str(obj):
    if isinstance(obj, bytes):
        return str(obj, encoding='utf-8')
    if isinstance(obj, (datetime.datetime,)):
        return obj.isoformat()
    if isinstance(obj, (decimal.Decimal,)):
        return str(obj)
    if hasattr(obj, '__dict__'):
        # 过滤掉空字符串属性
        filtered_dict = {}
        for key, value in obj.__dict__.items():
            if not value and value != 0:
                continue
            # 如果值是字符串类型且为空，则不包含该属性
            if not (isinstance(value, str) and (value == '' or value == 'null' )):
                filtered_dict[key] = value
        return filtered_dict
    return None


class jsonHelper:

    @staticmethod
    def toJson(obj):

        # 自定义序列化函数
        def custom_serializer(o):
            if isinstance(o, datetime.datetime):  # 处理datetime类型
                return o.strftime("%Y-%m-%d %H:%M:%S")
            elif isinstance(o, datetime.date):    # 可选：处理date类型
                return o.strftime("%Y-%m-%d")
            else:
                # 其他类型使用原decode函数（需确保decode存在）
                return decode(o)  # 保留原有逻辑处理其他类型

        ret = json.dumps(
            obj, 
            default=custom_serializer,
            ensure_ascii=False
        ).encode('utf-8')
        
        if isinstance(ret, bytes):
            return str(ret, encoding='utf-8')
        return ret

    @staticmethod
    def toSimpleJson(obj):
        """
        将对象转成JSON字符串，如果对象中某个字符串类型属性的值为空，则不序列化该属性
        :param obj: 要序列化的对象
        :return: JSON字符串
        """
        ret = json.dumps(obj, default=decode_filter_empty_str,
                         ensure_ascii=False).encode('utf-8')
        if isinstance(ret, bytes):
            return str(ret, encoding='utf-8')
        return ret

    @staticmethod
    def deJson(objStr):
        if len(objStr) == 0:
            dic = {}
            return JSONObject(dic)
        try:
            ret = json.loads(objStr, object_hook=JSONObject)
            return ret
        except Exception as err:
            logging.error('json反序列化失败' + str(err))
            dic = {}
            return JSONObject(dic)
