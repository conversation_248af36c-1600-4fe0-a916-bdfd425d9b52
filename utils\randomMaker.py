#随机数生成类

import random
import math
import datetime

from utils.xuehuaid import *


class randomMaker:

    worker = Id<PERSON>orker(random.randint(1, 30), random.randint(1, 30), 0)

    # 生成指定长度随机数
    @staticmethod
    def makeRandom(length):
        start = 1
        if length > 1:
            start = math.pow(10, length - 1)
        end = math.pow(10, length + 1) - 1
        rdm = random.randint(start, end)
        return str(rdm)

    @staticmethod
    def makeRandomNo():
        rdm = randomMaker.worker.get_id()
        return str(rdm)

    # 生成请求id
    @staticmethod
    def makeRequestId():
        time_str = datetime.datetime.now().strftime('%Y%m%d')
        rdm = randomMaker.worker.get_id()
        return time_str + str(rdm)

    # 生成随机状态(JH_01 ~ JH_05)
    @staticmethod
    def makeRandomStatus():
        return 'JH_0' + str(random.randint(1, 5))
