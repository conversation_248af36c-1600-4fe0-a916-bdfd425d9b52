
import hashlib


class signHelper:

    @staticmethod
    def makeMd5Sign(secret : str, parm_dict : dict) -> str:
        if not parm_dict or not secret:
            return ''
        # 直接遍历字典的键（Python 3.7+ 保留插入顺序，低版本可能无序）
        param_list = [f"{key}={value}" for key, value in parm_dict.items()]
        # 拼接键值对（按字典原始顺序）
        param_str = '&'.join(param_list)
        # 添加签名密钥
        sign_str = (secret + param_str + secret).lower()
        # 计算MD5哈希值
        md5 = hashlib.md5()
        md5.update(sign_str.encode('utf-8'))
        # 返回十六进制的小写字符串
        return md5.hexdigest()
        
